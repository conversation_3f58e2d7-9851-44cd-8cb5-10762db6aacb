2025-09-24 09:42:26,118 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 09:42:26,119 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 09:42:26,118 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 09:42:29,026 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 09:42:41,306 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 09:42:41,309 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 09:42:41,311 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 09:42:41,313 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 09:42:41,324 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 09:42:41,648 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 09:42:41,686 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 09:42:42,858 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 09:42:43,002 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 09:42:43,095 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 09:42:43,175 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 09:42:43,237 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 09:42:43,298 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 09:42:43,358 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 09:42:43,676 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 09:43:19,993 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 09:43:20,154 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 09:43:26,524 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 09:43:26,536 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/schedulePlan/SchedulePlanList
2025-09-24 09:43:30,986 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 09:43:31,093 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 09:43:35,688 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 09:43:35,703 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/schedulePlan/SchedulePlanList
2025-09-24 09:43:37,240 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 09:43:37,361 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 09:44:15,537 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 09:44:15,537 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 09:44:15,540 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 09:44:15,554 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 09:44:15,667 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 09:44:15,685 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 09:44:15,793 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 09:44:15,895 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 09:44:15,925 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 09:44:16,170 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 09:44:16,349 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 09:44:16,436 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 09:44:16,585 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 09:44:16,653 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 09:44:16,824 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 09:44:16,909 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 09:44:16,974 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 09:44:17,047 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 09:44:17,791 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 09:44:18,246 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 09:44:18,307 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 09:44:18,346 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 09:44:18,529 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 09:44:22,363 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 09:44:22,400 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/schedulePlan/SchedulePlanList
2025-09-24 09:44:34,985 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 09:44:40,492 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970417710129098753
2025-09-24 09:44:41,116 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsersByGroupCode
2025-09-24 09:44:41,117 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:44:41,123 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:44:41,126 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 09:44:41,132 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-24 09:44:41,182 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 09:44:41,201 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 09:44:41,265 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/pda_process
2025-09-24 09:44:41,409 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:44:41,409 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:44:41,488 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970417710129098753
2025-09-24 09:44:41,644 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:44:41,645 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:45:23,058 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 09:45:23,061 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/optionsList/experimentProject
2025-09-24 09:45:23,061 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/TestDataDictionary
2025-09-24 09:45:23,062 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_fieldName
2025-09-24 09:45:23,079 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/testDataDictionaryList
2025-09-24 09:45:23,171 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 09:45:23,385 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/optionsList/experimentProject
2025-09-24 09:45:23,456 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_fieldName
2025-09-24 09:45:59,881 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/testDataDictionaryList
2025-09-24 09:46:18,451 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970417710129098753
2025-09-24 09:46:18,908 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsersByGroupCode
2025-09-24 09:46:18,913 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:46:18,913 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:46:18,914 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 09:46:18,967 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:46:18,982 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:46:19,013 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:46:19,018 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970417710129098753
2025-09-24 09:46:19,057 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 09:47:28,376 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/testDataDictionaryList
2025-09-24 10:00:20,441 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 10:00:20,454 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 10:00:20,547 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 10:00:20,762 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 10:00:23,328 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 10:00:23,330 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/optionsList/experimentProject
2025-09-24 10:00:23,337 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 10:00:23,342 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_fieldName
2025-09-24 10:00:23,349 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/TestDataDictionary
2025-09-24 10:00:23,369 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/testDataDictionaryList
2025-09-24 10:00:23,565 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 10:00:23,967 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/testDataDictionary/optionsList/experimentProject
2025-09-24 10:00:24,054 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_fieldName
2025-09-24 10:00:46,505 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-24 10:00:46,506 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/workstation
2025-09-24 10:00:46,508 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_status
2025-09-24 10:00:46,508 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_team
2025-09-24 10:00:46,518 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_isApproval
2025-09-24 10:00:46,519 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardProcessManagement
2025-09-24 10:00:46,597 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/mpPdaCalcRls
2025-09-24 10:00:46,609 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/totalNoncompliantCount
2025-09-24 10:00:46,599 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_confirmationOfCapability
2025-09-24 10:00:46,653 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-24 10:00:46,862 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/workstation
2025-09-24 10:00:46,947 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_team
2025-09-24 10:00:46,983 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_isApproval
2025-09-24 10:00:47,022 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/department
2025-09-24 10:00:47,058 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_confirmationOfCapability
2025-09-24 10:00:47,267 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/standardProcessManagementList
2025-09-24 10:17:22,137 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 10:17:27,329 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970417710129098753
2025-09-24 10:17:27,963 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsersByGroupCode
2025-09-24 10:17:27,967 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-24 10:17:27,968 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 10:17:27,968 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:17:27,983 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 10:17:27,985 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:17:28,053 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/pda_process
2025-09-24 10:17:28,101 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 10:17:28,304 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970417710129098753
2025-09-24 10:17:28,387 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:17:28,473 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:17:28,518 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:17:28,578 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:18:58,186 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 10:19:01,625 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 10:19:04,051 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 10:19:06,741 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970417710129098753
2025-09-24 10:19:07,224 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsersByGroupCode
2025-09-24 10:19:07,229 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:07,231 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:07,231 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 10:19:07,319 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:07,320 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:07,329 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970417710129098753
2025-09-24 10:19:07,385 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:07,391 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 10:19:14,292 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/submitTask
2025-09-24 10:19:33,090 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/submitTask
2025-09-24 10:34:20,172 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 10:34:20,183 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 10:34:20,237 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 10:34:20,348 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 10:34:22,935 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 10:34:22,945 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 10:34:22,948 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 10:34:22,951 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 10:34:22,953 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 10:34:34,086 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 10:34:34,086 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 10:34:34,086 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 10:34:34,087 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 10:34:34,215 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 10:34:34,228 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 10:34:34,355 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 10:34:34,458 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 10:34:34,504 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 10:34:34,549 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 10:34:34,586 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 10:34:34,622 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 10:34:34,656 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 10:34:34,935 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:35:56,607 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:36:04,359 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:36:14,178 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:36:30,989 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:36:48,160 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:37:24,716 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:37:32,040 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:37:49,074 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:37:50,528 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:37:55,636 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:24,750 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:30,402 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:31,057 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:40,114 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:48,528 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:53,735 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:38:56,602 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:39:10,246 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:39:16,202 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:47:57,967 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:49:51,104 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 10:49:51,122 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 10:49:51,178 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 10:49:51,357 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 10:49:59,914 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 10:49:59,917 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 10:49:59,918 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 10:50:00,456 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 10:52:12,037 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 10:52:12,051 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 10:52:12,099 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 10:52:12,461 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 10:52:14,842 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 10:52:14,844 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 10:52:14,848 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 10:52:14,849 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 10:52:14,853 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 10:52:15,061 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 10:52:15,133 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 10:52:17,251 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 10:52:17,438 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 10:52:17,933 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 10:52:18,525 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 10:52:18,690 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 10:52:18,965 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 10:52:20,459 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 10:52:21,175 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:52:30,636 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:53:11,992 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 10:53:12,001 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 10:53:12,027 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 10:53:12,235 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 10:53:14,203 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 10:53:14,206 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 10:53:14,208 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 10:53:14,209 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 10:53:14,213 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 10:53:14,415 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 10:53:14,437 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 10:53:14,625 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 10:53:14,837 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 10:53:14,967 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 10:53:15,009 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 10:53:15,056 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 10:53:15,104 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 10:53:15,151 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 10:53:15,494 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:53:18,295 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 10:54:52,848 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/splitProductionOrder
2025-09-24 11:00:07,817 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:00:09,321 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:02:09,481 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 11:02:09,490 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 11:02:09,560 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 11:02:09,779 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 11:02:11,492 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 11:02:11,493 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 11:02:11,493 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 11:02:11,497 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 11:02:11,502 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 11:02:11,656 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 11:02:11,663 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 11:02:11,843 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 11:02:12,119 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 11:02:12,221 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 11:02:12,291 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 11:02:12,338 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 11:02:12,385 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 11:02:12,431 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 11:02:12,667 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:02:26,306 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:08:28,542 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:08:39,488 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:08:53,390 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/splitProductionOrder
2025-09-24 11:13:48,539 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:13:49,952 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:13:51,085 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:00,863 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:06,093 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:07,267 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 11:14:07,278 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 11:14:07,352 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 11:14:07,908 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 11:14:10,653 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 11:14:10,671 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 11:14:10,823 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 11:14:10,942 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 11:14:14,106 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 11:14:14,107 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 11:14:14,108 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 11:14:14,111 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 11:14:14,119 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 11:14:14,611 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 11:14:14,634 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 11:14:16,719 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 11:14:16,814 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 11:14:18,544 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 11:14:18,598 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 11:14:18,658 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 11:14:18,743 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 11:14:18,768 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-24 11:14:18,775 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-24 11:14:18,776 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-24 11:14:18,776 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-24 11:14:18,972 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 11:14:19,183 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 11:14:20,010 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:20,069 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-24 11:14:20,198 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-24 11:14:20,863 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-24 11:14:21,074 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-24 11:14:21,227 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-24 11:14:21,944 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 11:14:22,020 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 11:14:22,108 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-24 11:14:22,163 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-24 11:14:23,139 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:28,512 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:30,744 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:36,446 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:39,677 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:49,956 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:52,335 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:14:56,842 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:02,532 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:03,095 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:14,548 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:15,736 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 11:15:15,890 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 11:15:15,894 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 11:15:15,906 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 11:15:15,906 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 11:15:16,355 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 11:15:16,357 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 11:15:16,368 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 11:15:16,372 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 11:15:16,475 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 11:15:18,139 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 11:15:18,238 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 11:15:18,305 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 11:15:18,351 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 11:15:18,428 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 11:15:18,481 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 11:15:18,549 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 11:15:18,609 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 11:15:18,972 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:15:21,198 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:15:24,614 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:15:27,720 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:15:30,646 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:15:42,726 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:43,871 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:15:56,617 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:06,981 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:14,072 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:18,317 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:25,892 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:16:41,324 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:16:48,362 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:52,275 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:16:54,787 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-24 11:20:21,146 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970687623812452354
2025-09-24 11:20:22,581 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:22,581 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:22,596 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1970687623812452354
2025-09-24 11:20:22,614 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 11:20:22,617 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 11:20:22,617 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 11:20:22,669 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 11:20:22,681 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-24 11:20:22,683 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/pda_process
2025-09-24 11:20:22,712 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 11:20:22,728 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 11:20:22,748 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 11:20:22,764 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 11:20:22,784 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/yes_or_no
2025-09-24 11:20:22,790 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:22,799 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:22,848 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970687623812452354
2025-09-24 11:20:22,865 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:22,887 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:20:41,747 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:23:34,025 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:00,333 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:20,777 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:31,661 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 11:27:31,669 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 11:27:31,719 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 11:27:32,003 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 11:27:34,390 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 11:27:34,419 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 11:27:34,429 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 11:27:34,437 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 11:27:34,449 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 11:27:34,465 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 11:27:34,466 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 11:27:34,573 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 11:27:34,573 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 11:27:34,573 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 11:27:34,619 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 11:27:34,802 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 11:27:34,929 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 11:27:35,098 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 11:27:35,217 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 11:27:35,278 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 11:27:35,334 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 11:27:35,396 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 11:27:35,447 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 11:27:36,086 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:38,405 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:40,378 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 11:27:41,763 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970688053149798401
2025-09-24 11:27:43,947 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:27:43,989 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1970688053149798401
2025-09-24 11:27:44,079 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:27:44,090 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 11:27:44,091 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 11:27:44,091 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 11:27:44,121 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 11:27:44,236 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 11:27:44,238 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:27:44,253 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 11:27:44,293 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970688053149798401
2025-09-24 11:27:44,753 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:27:45,192 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:27:45,278 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 11:29:24,464 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 11:29:24,501 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SalesOutbound
2025-09-24 13:03:51,168 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 13:03:51,180 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 13:03:51,218 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 13:03:51,232 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 13:03:51,256 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 13:03:51,381 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 13:03:51,587 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 13:03:51,600 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 13:04:00,060 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 13:04:00,065 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SalesOutbound
2025-09-24 13:04:00,076 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 13:04:01,436 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 13:04:01,468 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 13:04:01,476 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 13:04:01,476 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 13:04:01,484 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 13:04:01,510 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 13:04:01,526 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 13:04:01,544 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 13:04:01,563 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 13:04:01,579 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 13:04:01,598 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 13:04:01,830 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 13:04:01,907 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 13:04:02,000 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 13:04:02,048 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 13:04:02,109 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 13:04:02,169 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 13:04:02,230 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 13:04:02,290 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 13:04:03,069 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:45:50,051 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:46:59,332 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:48:01,559 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:48:32,509 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:49:45,339 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:54:28,692 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:54:29,901 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:54:37,961 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:59:43,733 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 13:59:48,231 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 14:01:37,960 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 14:03:48,401 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 14:03:50,297 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 14:13:30,844 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 14:13:30,852 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 14:13:30,907 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 14:13:31,047 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 14:13:35,208 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 14:13:35,244 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 14:13:35,248 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 14:13:35,271 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 14:13:35,338 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 14:13:35,357 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 14:13:35,357 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 14:13:35,425 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 14:13:35,464 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 14:13:35,518 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 14:13:35,518 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 14:13:35,954 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 14:13:36,010 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 14:13:36,117 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 14:13:36,163 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 14:13:36,260 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 14:13:36,308 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 14:13:36,358 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 14:13:36,411 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 14:13:37,009 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 14:55:44,605 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 14:55:51,016 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 14:55:57,963 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dic
2025-09-24 14:55:58,274 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 14:56:23,915 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dic
2025-09-24 14:56:36,878 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 14:56:42,864 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:57:07,583 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail
2025-09-24 14:57:15,277 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:57:26,677 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 14:57:37,944 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:57:42,503 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail
2025-09-24 14:57:45,465 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:57:58,193 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail
2025-09-24 14:57:58,199 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:57:58,885 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:58:06,269 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/delete/
2025-09-24 14:58:08,902 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1967820911166070785
2025-09-24 14:59:57,604 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 14:59:57,611 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 14:59:57,612 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 14:59:57,615 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 14:59:57,616 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 14:59:57,633 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 14:59:57,685 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 14:59:57,745 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 14:59:57,869 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 14:59:57,946 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 14:59:58,162 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 14:59:58,246 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 14:59:59,481 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 14:59:59,565 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 14:59:59,766 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 14:59:59,838 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 14:59:59,887 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 14:59:59,933 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 15:00:00,282 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:00:09,000 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-24 15:00:09,001 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 15:00:09,003 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-24 15:00:09,007 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-24 15:00:09,035 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-24 15:00:09,036 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-24 15:00:09,220 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-24 15:00:09,424 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-24 15:00:09,592 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 15:00:09,650 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-24 15:00:09,703 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-24 15:05:12,360 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-24 15:06:01,286 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 15:06:03,273 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 15:10:48,472 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-24 15:10:48,480 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 15:10:48,480 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-24 15:10:48,481 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-24 15:10:48,503 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-24 15:10:48,530 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-24 15:10:48,699 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-24 15:10:48,840 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-24 15:10:50,845 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 15:10:50,927 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-24 15:10:50,992 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_deviceTypeName
2025-09-24 15:10:53,711 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/importTemplate
2025-09-24 15:11:14,221 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 15:11:14,232 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 15:11:14,241 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 15:11:14,244 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 15:11:14,245 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 15:11:14,244 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 15:11:14,366 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 15:11:14,403 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 15:11:14,451 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 15:11:14,576 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 15:11:14,711 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 15:11:14,810 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 15:11:15,296 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 15:11:15,394 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 15:11:15,507 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 15:11:15,590 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 15:11:15,683 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 15:11:15,750 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 15:11:16,271 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:11:19,531 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:11:28,268 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:18:10,395 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:18:13,062 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:18:19,077 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-24 15:18:53,649 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1970745421158670337
2025-09-24 15:18:54,358 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/abnormalDetailsBySourceId/1970745421158670337
2025-09-24 15:18:54,375 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 15:18:54,376 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-24 15:18:54,376 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:18:54,376 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:18:54,377 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 15:18:54,462 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/pda_process
2025-09-24 15:18:54,462 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 15:18:54,488 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 15:18:54,541 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 15:18:54,565 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 15:18:54,566 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 15:18:54,566 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 15:18:54,626 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:18:54,649 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:18:54,838 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/customerProcessScheme/getCustomerExperimentProjectSelectByWorkOrderId/1970737143653588994
2025-09-24 15:18:54,838 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1970745421158670337
2025-09-24 15:18:54,991 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:18:54,992 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-24 15:23:42,299 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-24 15:23:50,048 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:34:43,322 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:34:44,418 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:34:46,076 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:35:18,278 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:35:58,766 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:36:01,866 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-24 15:36:08,408 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 15:37:59,172 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Cardinventory
2025-09-24 15:37:59,172 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-24 15:37:59,179 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/cardinventory/cardinventoryList
2025-09-24 15:37:59,311 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-24 15:38:57,747 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-24 15:38:57,758 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-24 15:38:57,798 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 15:38:58,143 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 15:39:00,245 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-24 15:39:00,261 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/Cardinventory
2025-09-24 15:39:00,277 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 15:39:00,278 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/cardinventory/cardinventoryList
2025-09-24 15:39:00,543 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/cardinventory_dutBoardType
2025-09-24 15:39:09,348 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 15:39:11,820 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-24 16:01:08,870 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/cardinventory/importTemplate
2025-09-24 16:01:46,313 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-24 16:01:46,318 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-24 16:01:46,319 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-24 16:01:46,330 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-24 16:01:46,717 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-24 16:01:46,969 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-24 16:01:47,027 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-24 16:01:47,116 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-24 16:01:47,169 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-24 16:01:47,225 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 16:01:47,395 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-24 16:01:47,437 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-24 16:01:50,159 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 16:01:50,174 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 16:01:50,174 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 16:01:50,174 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 16:01:50,174 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 16:01:50,207 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-24 16:01:50,312 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 16:01:50,443 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 16:01:50,452 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 16:01:50,453 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 16:01:50,476 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-24 16:01:50,537 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-24 16:01:50,754 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-24 16:01:50,978 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-24 16:01:51,060 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-24 16:01:51,111 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-24 16:02:00,062 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInventory/productInventoryList
2025-09-24 16:02:00,080 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductInventory
2025-09-24 16:02:00,239 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_inventory_diskBurningData
2025-09-24 16:02:00,394 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_whetherToEnterComponents
2025-09-24 16:02:00,643 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_inventory_status
2025-09-24 16:02:00,813 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_report
2025-09-24 16:02:02,581 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInventory/productInventoryList
2025-09-24 16:02:04,386 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SalesOutbound
2025-09-24 16:02:04,398 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:02:06,887 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/MaterialsLibrary
2025-09-24 16:02:06,897 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/materialsLibrary/materialsLibraryList
2025-09-24 16:03:31,265 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-09-24 16:03:31,267 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-09-24 16:03:31,267 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-09-24 16:03:31,278 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-09-24 16:03:31,284 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-09-24 16:03:31,446 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-09-24 16:03:31,647 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-09-24 16:03:32,017 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 16:03:32,702 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 16:03:40,826 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-09-24 16:10:45,330 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SalesOutbound
2025-09-24 16:10:45,340 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:15:33,467 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:20:09,595 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:23:00,902 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductInventory
2025-09-24 16:23:00,921 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productInventory/productInventoryList
2025-09-24 16:23:01,074 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_inventory_diskBurningData
2025-09-24 16:23:01,393 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_whetherToEnterComponents
2025-09-24 16:23:01,515 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_inventory_status
2025-09-24 16:23:01,679 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_report
2025-09-24 16:23:26,119 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SalesOutbound
2025-09-24 16:23:26,128 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:23:32,551 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/salesOutbound/salesOutboundList
2025-09-24 16:41:39,168 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 16:41:39,177 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 16:41:39,180 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 16:41:39,184 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 16:41:39,192 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 16:41:39,210 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-24 16:41:40,044 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 16:41:40,060 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 16:41:40,060 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 16:41:40,113 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 16:41:40,116 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-24 16:41:40,216 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-24 16:41:40,319 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-24 16:41:40,404 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-24 16:41:40,457 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-24 16:41:40,513 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-24 16:48:21,380 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 16:48:21,388 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 16:48:21,391 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-24 16:48:21,395 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 16:48:21,395 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 16:48:21,397 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 16:48:21,501 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 16:48:21,531 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 16:48:21,533 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 16:48:21,534 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 16:48:21,705 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 16:48:21,794 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 16:48:22,240 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 16:48:22,309 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 16:48:22,445 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 16:48:22,519 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 16:48:22,582 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 16:48:22,647 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 16:48:22,978 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:49:44,120 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:49:46,343 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:49:48,518 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:49:51,678 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:49:53,669 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 16:57:11,258 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-24 16:57:12,603 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-24 16:57:20,162 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-24 16:57:20,378 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-24 16:57:20,494 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-24 16:57:21,395 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-24 16:57:21,561 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-24 16:57:22,306 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 16:57:22,311 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-24 16:57:22,318 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 16:57:22,318 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 16:57:22,332 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/home/<USER>
2025-09-24 16:57:50,779 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/isExternalPerson/1970756939002560513
2025-09-24 16:57:50,797 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-09-24 16:57:50,804 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-24 16:57:50,804 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-24 16:57:50,804 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-24 16:57:50,804 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-24 16:57:51,051 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-24 16:57:51,060 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-24 16:57:51,061 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-24 16:57:51,126 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-24 16:57:51,230 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-24 16:57:51,345 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-24 16:57:51,411 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-24 16:57:51,473 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-24 16:57:51,547 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_grouping
2025-09-24 16:57:51,607 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-24 16:57:51,656 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-24 16:57:51,709 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-24 16:57:51,987 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-24 17:00:17,263 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
