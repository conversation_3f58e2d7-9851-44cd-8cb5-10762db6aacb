package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.mq.producer.MessageProducer;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.business.utils.DateTimeUtil;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.service.DicDetailService;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.hpsf.Decimal;
import org.aspectj.apache.bcel.generic.RET;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.modules.business.service.dto.MessageManagementDTO;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * 生产任务 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productionTask")
//@RefreshScope
@Slf4j
public class ProductionTaskServiceImpl implements ProductionTaskService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private ProductionTaskMapper productionTaskMapper;
    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

    @Autowired
    private ProductionTaskTestDataMapper productionTaskTestDataMapper;

    @Autowired
    private ProdTaskEqInfoMapper prodTaskEqInfoMapper;

    @Autowired
    private ProductionTaskAttachmentsMapper productionTaskAttachmentsMapper;

    @Autowired
    private ProdTaskOpHistMapper prodTaskOpHistMapper;
    @Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;
    @Autowired
    private ProductListMapper productListMapper;
    @Autowired
    private DicDetailService dicDetailService;

    @Autowired
    private AbnormalfeedbackMapper abnormalfeedbackMapper;

    @Autowired
    private OutsourcingMapper outsourcingMapper;

    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    @Autowired
    private AbnormalfeedbackService abnormalfeedbackService;

    @Autowired
    private OutsourcingService outsourcingService;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private SysGroupService sysGroupService;

    @Autowired
    private WorkstationService workstationService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private ProdTaskOpHistService prodTaskOpHistService;

    @Autowired
    private ProductionOrderProcessTestService productionOrderProcessTestService;

    @Autowired
    private TestDataDictionaryMapper testDataDictionaryMapper;

    @Autowired
    private StandardProcessManagementMapper standardProcessManagementMapper;
    @Autowired
    private CustomerProcessSchemeMapper customerProcessSchemeMapper;
    @Autowired
    private CustomerExperimentProjectMapper customerExperimentProjectMapper;

    @Autowired
    private MessageManagementService messageManagementService;

    @DubboReference
    private SysUserService sysUserService;

    public ProductionTaskServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<ProductionTaskVO>> findProductionTaskPage(ProductionTaskDTO dto) {

        queryExportCommon(dto);

        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<ProductionTaskVO> productionTasks = productionTaskMapper.selectProductionTaskPage(dto);
        TorchResponse<List<ProductionTaskVO>> response = new TorchResponse<List<ProductionTaskVO>>();
        response.getData().setData(productionTasks);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(productionTasks.getTotal());
        return response;
    }

    /**
     * 查询和导出权限
     *
     * @param dto
     */
    private void queryExportCommon(ProductionTaskDTO dto) {

        if (BusinessConstant.ADMIN.equals(SecurityContextHolder.getCurrentUserName())) {
            dto.setIsAdmin(true);
        } else {
            dto.setIsAdmin(false);
        }

        String groupCode = "";
        SysGroup sysGroup = sysGroupService.selectById(SecurityContextHolder.getCurrentUserGroupId());
        if (sysGroup != null) {
            groupCode = sysGroup.getGroupCode();
        }
        //查询当前用户角色
        List<String> roles = sysUserService.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());

        if (BusinessConstant.PRODUCTIONTASK_TODO.equals(dto.getToDoOrAll())) {
            if (StringUtils.isEmpty(dto.getStatus())) {
                dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI + "," +
                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG + "," +
                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING + "," +
                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI + "," +
                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE);
            } else {
                //如果传的status不在这几个钟就返回空
                if (!dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI) &&
                        !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG) &&
                        !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING) &&
                        !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI) &&
                        !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE)) {
                    dto.setStatus("999");
                }
            }
        }

        //1.admin 或调度可以看待办和全部
        if (dto.getIsAdmin() || roles.contains(DicConstant.Role.ROLE_SHENGCHAN_DIAODU) || roles.contains(DicConstant.Role.ROLE_KEKAOXING_DIAODU)) {
            return;
        }


        //用户部门和班组
        List<String> groupCodes = sysGroupService.selectGroupCodeByGroupId(SecurityContextHolder.getCurrentUserGroupId());

        //2.审批逻辑
        if (roles.contains(BusinessConstant.ROLE_RENWUSHENPIREN)) {
            if (groupCodes.contains(DicConstant.Group.GROUP_KEKAOXING)) {
                dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserId());
            } else {
                dto.setBelongingGroup(groupCode);
            }
//            if (BusinessConstant.PRODUCTIONTASK_TODO.equals(dto.getToDoOrAll())) {
//                dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE);
//            } else {
//                dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE + "," +
//                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);
//            }

//            return;
        }
        //3.班组
        if (BusinessConstant.PRODUCTIONTASK_TODO.equals(dto.getToDoOrAll())) {
//            if (!roles.contains(BusinessConstant.ROLE_SHENGCHANBUBANZUZHANG) && !roles.contains(BusinessConstant.ROLE_KEKAOXINGBANZUZHANG))
//            {
////                //非班组长
////                if (StringUtils.isEmpty(dto.getStatus())) {
////                    dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI + "," +
////                            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG + "," +
////                            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING + "," +
////                            DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI);
////                } else {
////                    //如果传的status不在这几个钟就返回空
////                    if (!dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI) &&
////                            !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG) &&
////                            !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING) &&
////                            !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI)) {
////                        dto.setStatus("999");
////                    }
////                }
//            }
        } else {
            //全部页面：对班组长可查看该班组对应的所有任务；对调度员可查看所属部门的所有任务。
            if (!roles.contains(BusinessConstant.ROLE_SHENGCHANBUBANZUZHANG) &&
                    !roles.contains(BusinessConstant.ROLE_KEKAOXINGBANZUZHANG)) {
                dto.setStatus("999");
            }
        }
        dto.setBelongingTeamId(groupCode);
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(ProductionTaskDTO productionTaskDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productionTaskDto.getCodexTorchDeleted())) {
            productionTaskDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productionTaskDto.getId();
        ProductionTask entity = new ProductionTask();
        BeanUtils.copyProperties(productionTaskDto, entity);
        entity.setReportingTime0(new Timestamp(System.currentTimeMillis()));
        if (null != entity.getCompletionTime6()) {
            entity.setActualEndTime(new Timestamp(entity.getCompletionTime6().getTime()));

            //结束时间不能小于开始时间
            if (entity.getActualStartTime() != null && entity.getActualEndTime() != null) {
                if (entity.getActualEndTime().before(entity.getActualStartTime())) {
                    //开始时间格式化
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String startTime = sdf.format(entity.getActualStartTime());
                    throw new ServiceException("完成时间不能小于开始时间，开始时间为：" + startTime);
                }
            }


            // 2.生产任务设置完成时间时增加更新aps_schedule_plan_step中的actualendtime
            if (!HuatekTools.isEmpty(entity.getApsSchedulePlanStepId())) {
                productionTaskMapper.updateApsSchedulePlanStepActualEndTime(
                        entity.getApsSchedulePlanStepId(),
                        entity.getActualEndTime());
            }
        }
        //entity.setActualEndTime(new Timestamp(entity.getCompletionTime6().getTime()));
        // entity.setRecordChangeStamp(productionTaskDto.getRecordChangeStamp());
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

        //添加设备时需校验设备编号必填
        if (!CollectionUtils.isEmpty(productionTaskDto.getProdTaskEqInfoList())) {
            for (ProdTaskEqInfoDTO eqInfoDto : productionTaskDto.getProdTaskEqInfoList()) {
                if (HuatekTools.isEmpty(eqInfoDto.getDeviceSerialNumber())) {
                    throw new ServiceException("设备编号不能为空");
                }
            }
        }

        // 保存或更新主表
        if (HuatekTools.isEmpty(id)) {

            //通过工单编号和工序编号查询如果存在就提示数据已存在
            if (!HuatekTools.isEmpty(productionTaskDto.getWorkOrderNumber()) && !HuatekTools.isEmpty(productionTaskDto.getProcessCode())) {
                QueryWrapper<ProductionTask> wrapper = new QueryWrapper<>();
                wrapper.eq("work_order_number", productionTaskDto.getWorkOrderNumber());
                wrapper.eq("process_code", productionTaskDto.getProcessCode());
                wrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
                Long count = productionTaskMapper.selectCount(wrapper);
                if (count != null && count > 0) {
                    throw new ServiceException("数据已存在");
                }
            }

            TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
            entity.setTaskNumber(response.getData().getData().toString());
            entity.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI);
            productionTaskMapper.insert(entity);
        } else {

            //是否pdf预警
//            if (!BusinessConstant.PDA_PROCESS_NAME.equals(entity.getProcessName2())) {
//                setPdaWarning(entity);
//            }
            //如果非PDA工序，计算值进行预警
            if (!isPDA(entity.getProcessCode())) {
                setPdaWarning(entity);
            }
//            if (isPDA(entity.getProcessCode())) {
//                entity.setPda(calculatePda(entity));
//            }


            productionTaskMapper.updateById(entity);
            // 更新时先删除子表数据
            deleteChildTableData(entity.getTaskNumber());
            // 保存子表数据
            saveChildTableData(productionTaskDto, entity.getTaskNumber());
        }


        TorchResponse response = new TorchResponse();
        ProductionTaskVO vo = new ProductionTaskVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    //通过编码判断是否pda
    private boolean isPDA(String processCode) {
        TorchResponse<List<Map<String, String>>> response = dicDetailService.findDicDetail(BusinessConstant.PDA_PROCESS_CODE);
        if (response != null && response.getData().getData() != null) {
            for (Map<String, String> map : response.getData().getData()) {
                if (map.containsValue(processCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 设置PDA预警
     *
     * @param entity
     */
    private void setPdaWarning(ProductionTask entity) {

        CustomerExperimentProject customerExperimentProject = getCustomerExperimentProject(entity);
        if (customerExperimentProject == null || customerExperimentProject.getPda() == null) return;
        if (entity.getUnqualifiedQuantity() != null && entity.getInspectionQuantity2() != null) {
            BigDecimal unqualified = new BigDecimal(entity.getUnqualifiedQuantity().toString());
            BigDecimal total = new BigDecimal(entity.getInspectionQuantity2().toString());
            if (total.compareTo(BigDecimal.ZERO) > 0) { // 避免除零
                BigDecimal ratio = unqualified.divide(total, 4, RoundingMode.HALF_UP); // 保留4位小数

                BigDecimal pda = customerExperimentProject.getPda().divide(BigDecimal.valueOf(100));
                if (!HuatekTools.isEmpty(customerExperimentProject.getPda()) && ratio.compareTo(pda) >= 0) {
                    log.info("PDA预警：{}", ratio.toString());
                    entity.setPdaWarning(DicConstant.CommonDic.DIC_YES);
                } else {
                    entity.setPdaWarning(DicConstant.CommonDic.DIC_NO);
                }
            } else {
                entity.setPdaWarning(DicConstant.CommonDic.DIC_NO);
            }
        }
    }



    @Nullable
    private CustomerExperimentProject getCustomerExperimentProject(ProductionTask entity) {
        return customerExperimentProjectMapper.selectById(entity.getCustomerExperimentProjectId());
//        QueryWrapper<ProductionOrder> orderQueryWrapper = new QueryWrapper<>();
//        orderQueryWrapper.eq("work_order_number", entity.getWorkOrderNumber());
//        ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(orderQueryWrapper);
//        if (productionOrder == null) {
//            return null;
//        }
//
//
//        QueryWrapper<CustomerProcessScheme> customerProcessSchemeQueryWrapper = new QueryWrapper<>();
//        customerProcessSchemeQueryWrapper.eq("work_order", productionOrder.getId());
//        CustomerProcessScheme customerProcessScheme = customerProcessSchemeMapper.selectOne(customerProcessSchemeQueryWrapper);
//        if (customerProcessScheme == null) {
//            return null;
//        }
//
//        QueryWrapper<CustomerExperimentProject> wrapper = new QueryWrapper<>();
//        wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", customerProcessScheme.getId());
//        wrapper.eq("process_code3", entity.getProcessCode());
//        wrapper.eq("execution_sequence", entity.getExecutionSequence());
//        wrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
//
//        CustomerExperimentProject customerExperimentProject = customerExperimentProjectMapper.selectOne(wrapper);
//        if (customerExperimentProject == null) {
//            return null;
//        }
//        return customerExperimentProject;
    }

    private BigDecimal calculatePda(ProductionTask entity) {

        //当前工序信息

        CustomerExperimentProject customerExperimentProject = getCustomerExperimentProject(entity);
        if (customerExperimentProject == null) return new BigDecimal(0);

        //通过当前工序信息查询多工序PDA计算规则
        BigDecimal unqualifiedQuantity = getUnqualifiedQuantity(entity.getWorkOrderNumber(), customerExperimentProject.getMpPdaCalcRls());

        //通过当前工序信息查询不合格数量总和/
        BigDecimal qualifiedQuantity = getQualifiedQuantity(entity.getWorkOrderNumber(), customerExperimentProject.getTotalNoncompliantCount());

        //计算PDA值 unqualifiedQuantity/qualifiedQuantity*100 保留四位小数



        //计算PDA值
        BigDecimal pda = unqualifiedQuantity.divide(qualifiedQuantity, 4, RoundingMode.HALF_UP);

        //pda=pda乘100
        pda = pda.multiply(new BigDecimal(100));

        BigDecimal pda2 = pda.setScale(2, RoundingMode.HALF_UP);

        return pda2;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<ProductionTaskVO> findProductionTask(String id) {
        ProductionTaskVO vo = new ProductionTaskVO();
        if (!HuatekTools.isEmpty(id)) {
            ProductionTask entity = productionTaskMapper.selectById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);

            // 通过taskNumber查询子对象数据
            String taskNumber = entity.getTaskNumber();
            getTaskProcessDataByTaskNumber(taskNumber, vo,entity.getWorkOrderNumber(),entity.getProcessCode());

            //通过工单编号查询到工单id
            QueryWrapper<ProductionOrder> wrapper = new QueryWrapper<>();
            wrapper.eq("work_order_number", entity.getWorkOrderNumber())
                    .eq("codex_torch_deleted", Constant.DEFAULT_NO);
            ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(wrapper);
            if (productionOrder != null) {
                vo.setProductionOrderId(productionOrder.getId());
            }



            //获取多工序PDA计算规则和不合格数量总和/
            getMpPdaCalcRlsAndTotalNoncompliantCount(vo);
        }
        TorchResponse<ProductionTaskVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    /**
     * 获取多工序PDA计算规则和不合格数量总和/
     * @param vo
     */
    private void getMpPdaCalcRlsAndTotalNoncompliantCount(ProductionTaskVO  vo) {
        if (!HuatekTools.isEmpty(vo.getCustomerExperimentProjectId())) {
            CustomerExperimentProject customerExperimentProject = customerExperimentProjectMapper.selectById(vo.getCustomerExperimentProjectId());
            if (customerExperimentProject != null) {
                vo.setMpPdaCalcRls(customerExperimentProject.getMpPdaCalcRls());
                vo.setTotalNoncompliantCount(customerExperimentProject.getTotalNoncompliantCount());
            }
        }
    }

    public TorchResponse selectProductionTaskByNumber(String taskNumber){
        ProductionTaskVO vo  = new ProductionTaskVO();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("task_number",taskNumber);
        wrapper.eq("codex_torch_deleted",DicConstant.CommonDic.DIC_NO);
        ProductionTask entity = productionTaskMapper.selectOne(wrapper);
        BeanUtils.copyProperties(entity, vo);
        getTaskProcessDataByTaskNumber(taskNumber, vo,entity.getWorkOrderNumber(),entity.getProcessCode());
        TorchResponse<ProductionTaskVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }
    public void getTaskProcessDataByTaskNumber(String taskNumber,ProductionTaskVO vo,String workOrderNumber,String processCode){
        if (!HuatekTools.isEmpty(taskNumber)) {
            // 查询试验数据
            ProductionTaskTestDataDTO testDataDTO = new ProductionTaskTestDataDTO();
            testDataDTO.setTaskNumber(taskNumber);
            List<ProductionTaskTestDataVO> testDataList = productionTaskTestDataMapper.selectProductionTaskTestDataList(testDataDTO);
            vo.setProductionTaskTestDataList(testDataList);

            // 查询设备信息
            ProdTaskEqInfoDTO eqInfoDTO = new ProdTaskEqInfoDTO();
            eqInfoDTO.setTaskNumber(taskNumber);
            List<ProdTaskEqInfoVO> eqInfoList = prodTaskEqInfoMapper.selectProdTaskEqInfoList(eqInfoDTO);
            vo.setProdTaskEqInfoList(eqInfoList);

            // 查询附件信息
            ProductionTaskAttachmentsDTO attachmentsDTO = new ProductionTaskAttachmentsDTO();
            attachmentsDTO.setTaskNumber(taskNumber);
            List<ProductionTaskAttachmentsVO> attachmentsList = productionTaskAttachmentsMapper.selectProductionTaskAttachmentsList(attachmentsDTO);
            vo.setProdTaskAttachmentList(attachmentsList);

            // 查询操作历史
            ProdTaskOpHistDTO opHistDTO = new ProdTaskOpHistDTO();
            opHistDTO.setTaskNumber(taskNumber);
            List<ProdTaskOpHistVO> opHistList = prodTaskOpHistMapper.selectProdTaskOpHistList(opHistDTO);
            vo.setProdTaskOpHistDTOS(opHistList);

            //查询设备原始数据
            if (!HuatekTools.isEmpty(workOrderNumber) && !HuatekTools.isEmpty(processCode)) {
                TorchResponse<List<AttachmentVO>> attachmentResponse = attachmentService.selectAttachmentListByWorkOrderAndProcess(
                        workOrderNumber, processCode);
                if (attachmentResponse.getStatus() == Constant.REQUEST_SUCCESS && !CollectionUtils.isEmpty(attachmentResponse.getData().getData())) {
                    vo.setAttachmentVOList(attachmentResponse.getData().getData());
                }
            }
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<ProductionTask> productionTaskList = productionTaskMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionTask productionTask : productionTaskList) {
            productionTask.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productionTaskMapper.updateById(productionTask);
        }
        //productionTaskMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
            //初始化外键函数
            selectOptionsFuncMap.put("technicalCompetencyNumber", productionTaskMapper::selectOptionsByTechnicalCompetencyNumber);
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "capability_asset":
                    data = selectDataLinkageByTechnicalCompetencyNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }

    @Override
    public Map<String, String> selectDataLinkageByTechnicalCompetencyNumber(String capability_number) {
        return productionTaskMapper.selectDataLinkageByTechnicalCompetencyNumber(capability_number);
    }

    @Override
    @ExcelExportConversion(tableName = "production_task", convertorFields = "experiment_project_grouping#grouping,yes_or_no#pdaWarning,status,product_list_taskLevel#ticketLevel,experiment_project_testMethodology#testMethodology,test_data_dictionary_testType#testType,failureMode,prod_task_op_hist_reason#pauseReason")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionTaskVO> selectProductionTaskList(ProductionTaskDTO dto) {


        queryExportCommon(dto);

        return productionTaskMapper.selectProductionTaskList(dto);
    }

    /**
     * 导入生产任务数据
     *
     * @param productionTaskList 生产任务数据列表
     * @param unionColumns       作为确认数据唯一性的字段集合
     * @param isUpdateSupport    是否更新支持，如果已存在，则进行更新数据
     * @param operName           操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "production_task", convertorFields = "status,product_list_taskLevel#ticketLevel,experiment_project_testMethodology#testMethodology,test_data_dictionary_testType#testType,failureMode")
    public TorchResponse importProductionTask
    (List<ProductionTaskVO> productionTaskList, List<String> unionColumns, Boolean isUpdateSupport, String
            operName) {
        if (StringUtils.isNull(productionTaskList) || productionTaskList.size() == 0) {
            throw new ServiceException("导入生产任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductionTaskVO vo : productionTaskList) {
            try {
                ProductionTask productionTask = new ProductionTask();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }
                BeanUtils.copyProperties(vo, productionTask);
                QueryWrapper<ProductionTask> wrapper = new QueryWrapper();
                ProductionTask oldProductionTask = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn : unionColumns) {
                        try {
                            Field field = ProductionTaskVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProductionTask> oldProductionTaskList = productionTaskMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProductionTaskList) && oldProductionTaskList.size() > 1) {
                        productionTaskMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProductionTaskList) && oldProductionTaskList.size() == 1) {
                        oldProductionTask = oldProductionTaskList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProductionTask)) {
                    BeanValidators.validateWithException(validator, vo);
                    productionTaskMapper.insert(productionTask);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProductionTask, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    productionTaskMapper.updateById(oldProductionTask);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProductionTaskVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getTechnicalCompetencyNumber())) {
            List<String> technicalCompetencyNumberList = Arrays.asList(vo.getTechnicalCompetencyNumber().split(","));
            List<CapabilityAsset> list = capabilityAssetMapper.selectList(new QueryWrapper<CapabilityAsset>().in("capability_number", technicalCompetencyNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("技术能力编号=" + vo.getTechnicalCompetencyNumber() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    /**
     * 删除子表数据
     *
     * @param taskNumber 任务编号
     */
    private void deleteChildTableData(String taskNumber) {
        if (!HuatekTools.isEmpty(taskNumber)) {
            // 删除试验数据
            QueryWrapper<ProductionTaskTestData> testDataWrapper = new QueryWrapper<>();
            testDataWrapper.eq("task_number", taskNumber);
            productionTaskTestDataMapper.delete(testDataWrapper);

            // 删除设备信息
            QueryWrapper<ProdTaskEqInfo> eqInfoWrapper = new QueryWrapper<>();
            eqInfoWrapper.eq("task_number", taskNumber);
            prodTaskEqInfoMapper.delete(eqInfoWrapper);

            // 删除附件信息
            QueryWrapper<ProductionTaskAttachments> attachmentWrapper = new QueryWrapper<>();
            attachmentWrapper.eq("task_number", taskNumber);
            productionTaskAttachmentsMapper.delete(attachmentWrapper);
        }
    }

    /**
     * 保存子表数据
     *
     * @param productionTaskDto 生产任务DTO
     * @param taskNumber        任务编号
     */
    private void saveChildTableData(ProductionTaskDTO productionTaskDto, String taskNumber) {
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        String currentUserGroupId = SecurityContextHolder.getCurrentUserGroupId();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        // 保存试验数据
        if (!CollectionUtils.isEmpty(productionTaskDto.getProductionTaskTestDataList())) {
            for (ProductionTaskTestDataDTO testDataDto : productionTaskDto.getProductionTaskTestDataList()) {
                ProductionTaskTestData testData = new ProductionTaskTestData();
                BeanUtils.copyProperties(testDataDto, testData);
                testData.setTaskNumber(taskNumber);
                testData.setCodexTorchCreatorId(currentUserId);
                testData.setCodexTorchGroupId(currentUserGroupId);
                testData.setCodexTorchCreateDatetime(currentTime);
                testData.setCodexTorchUpdateDatetime(currentTime);
                testData.setCodexTorchDeleted(Constant.DEFAULT_NO);
                productionTaskTestDataMapper.insert(testData);
            }
        }

        // 保存设备信息
        if (!CollectionUtils.isEmpty(productionTaskDto.getProdTaskEqInfoList())) {
            for (ProdTaskEqInfoDTO eqInfoDto : productionTaskDto.getProdTaskEqInfoList()) {
                ProdTaskEqInfo eqInfo = new ProdTaskEqInfo();
                BeanUtils.copyProperties(eqInfoDto, eqInfo);
                eqInfo.setTaskNumber(taskNumber);
                eqInfo.setCodexTorchCreatorId(currentUserId);
                eqInfo.setCodexTorchGroupId(currentUserGroupId);
                eqInfo.setCodexTorchCreateDatetime(currentTime);
                eqInfo.setCodexTorchUpdateDatetime(currentTime);
                eqInfo.setCodexTorchDeleted(Constant.DEFAULT_NO);
                prodTaskEqInfoMapper.insert(eqInfo);
            }
        }

        // 保存附件信息
        if (!CollectionUtils.isEmpty(productionTaskDto.getProdTaskAttachmentList())) {
            for (ProductionTaskAttachmentsDTO attachmentDto : productionTaskDto.getProdTaskAttachmentList()) {
                ProductionTaskAttachments attachment = new ProductionTaskAttachments();
                BeanUtils.copyProperties(attachmentDto, attachment);
                attachment.setTaskNumber(taskNumber);
                attachment.setCodexTorchCreatorId(currentUserId);
                attachment.setCodexTorchGroupId(currentUserGroupId);
                attachment.setCodexTorchCreateDatetime(currentTime);
                attachment.setCodexTorchUpdateDatetime(currentTime);
                attachment.setCodexTorchDeleted(Constant.DEFAULT_NO);
                productionTaskAttachmentsMapper.insert(attachment);
            }
        }
    }

    private Map<String, String> getAllCascadeOptions
            (List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductionTaskListByIds(List<String> ids) {
        List<ProductionTaskVO> productionTaskList = productionTaskMapper.selectProductionTaskListByIds(ids);

        TorchResponse<List<ProductionTaskVO>> response = new TorchResponse<List<ProductionTaskVO>>();
        response.getData().setData(productionTaskList);
        response.setStatus(200);
        response.getData().setCount((long) productionTaskList.size());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse abnormalfeedback(AbnormalfeedbackDTO abnormalfeedbackDto) {

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        ProductionTask entity = productionTaskMapper.selectById(abnormalfeedbackDto.getSourceId());
        abnormalfeedbackDto.setSourceModule(BusinessConstant.PRODUCTION_TASK_MANAGE_MENU_NAME);
        abnormalfeedbackDto.setProductionWorkOrder(entity.getWorkOrderNumber());
        abnormalfeedbackDto.setCustomerProcessId(entity.getCustomerExperimentProjectId());
        TorchResponse<AbnormalfeedbackVO> torchResponse = abnormalfeedbackService.saveOrUpdate(abnormalfeedbackDto);
        AbnormalfeedbackVO vo = torchResponse.getData().getData();

        if (HuatekTools.isEmpty(entity.getAssocExceptionFeedbackNum())) {
            entity.setAssocExceptionFeedbackNum(vo.getAbnormalNumber());
        } else {
            entity.setAssocExceptionFeedbackNum(entity.getAssocExceptionFeedbackNum() + "," + vo.getAbnormalNumber());
        }

        productionTaskMapper.updateById(entity);

        // 自动通知关联工单的负责人
        notifyRelatedWorkOrderResponsiblePersons(entity.getWorkOrderNumber(), vo.getAbnormalNumber());

        return response;
    }

    /**
     * 通知设置当前工单为关联工单的其他工单的负责人
     *
     * @param currentWorkOrderNumber 当前工单号
     * @param abnormalNumber         异常反馈编号
     */
    private void notifyRelatedWorkOrderResponsiblePersons(String currentWorkOrderNumber, String abnormalNumber) {
        try {

            //通过currentWorkOrderNumber查询工单id
            LambdaQueryWrapper<ProductionOrder> poQueryWrapper = Wrappers.lambdaQuery(ProductionOrder.class)
                    .eq(ProductionOrder::getWorkOrderNumber, currentWorkOrderNumber);
            ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(poQueryWrapper);
            if (productionOrder == null) {
                return;
            }

            // 查询设置当前工单为关联工单的其他工单
            LambdaQueryWrapper<ProductionTask> queryWrapper = Wrappers.lambdaQuery(ProductionTask.class)
                    .eq(ProductionTask::getRelatedWorkOrder, productionOrder.getId())
                    .eq(ProductionTask::getCodexTorchDeleted, Constant.DEFAULT_NO)
                    .isNotNull(ProductionTask::getResponsiblePerson);

            List<ProductionTask> tasks = productionTaskMapper.selectList(queryWrapper);

            if (!CollectionUtils.isEmpty(tasks)) {

                Map<String, String> workOrderResponsiblePersonMap = new HashMap<>();
                tasks.forEach(task -> {
                    workOrderResponsiblePersonMap.put(task.getWorkOrderNumber(), task.getResponsiblePerson());
                });
                List<MessageManagement> messageList = new ArrayList<>();
                // 获取发送者ID
                String senderId = SecurityContextHolder.getCurrentUserId();
                Timestamp sendTime = new Timestamp(System.currentTimeMillis());
                workOrderResponsiblePersonMap.forEach((workOrderNumber, responsiblePerson) -> {

                    String messageContent = String.format("%s工单的关联工单%s有新的异常反馈，反馈编号为%s",
                            workOrderNumber, currentWorkOrderNumber, abnormalNumber);

                    MessageManagement message = new MessageManagement();
                    message.setMessageContent(messageContent);
                    message.setSendTime(sendTime);
                    message.setUserId(responsiblePerson);
                    message.setSenderId(senderId);
                    messageList.add( message);
                });
                // 批量保存消息
                if (!messageList.isEmpty()) {
                    messageManagementService.batchSaveMessage(messageList, 200);
                }
            }
        } catch (Exception e) {
            log.error("通知关联工单负责人失败", e);
        }
    }

    @Override
    public TorchResponse outsourcing(AddOrUpdateOutsourcingsDTO addOrUpdateOutsourcingsDTO, String token) {
        List<String> idList = addOrUpdateOutsourcingsDTO.getIdList();

        if (HuatekTools.isEmpty(idList)) {
            throw new ServiceException("外协任务ID列表不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        List<String> successIds = new ArrayList<>();
        List<String> failIds = new ArrayList<>();
        List<ProductionTask> productionTasks = productionTaskMapper.selectBatchIds(idList);
        for (ProductionTask item : productionTasks) {
            String id = item.getId();
            try {
                //查询出来对应生产工单
                LambdaQueryWrapper<ProductionOrder> queryWrapper = Wrappers.lambdaQuery(ProductionOrder.class)
                        .eq(ProductionOrder::getWorkOrderNumber, item.getWorkOrderNumber());
                ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(queryWrapper);
                AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO = addOrUpdateOutsourcingsDTO.getAddOrUpdateOutsourcingDTO();
                //如果生产工单存在，回填生产工单id
                if (productionOrder != null) {
                    addOrUpdateOutsourcingDTO.setOrderId(productionOrder.getId());
                }

                addOrUpdateOutsourcingDTO.setProcessId(id);

                addOrUpdateOutsourcingDTO.setEntireOrProcess(DicConstant.ProductionOrder.OUTSOURCING_TYPE_PROCESS);
                outsourcingService.apply(addOrUpdateOutsourcingDTO, token);

                ProductionTask productionTask = productionTaskMapper.selectById(addOrUpdateOutsourcingDTO.getProcessId());

                String previousStatus = productionTask.getStatus();
                if (productionTask != null) {
                    productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING);
                    productionTask.setPauseReason(DicConstant.ProductionOrder.PRODUCTION_PAUSE_REASON_WAIXIE);
                    productionTask.setTestMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING);
                    productionTaskMapper.updateById(productionTask);

                    insertOperationHistory(productionTask.getTaskNumber(),DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE,
                            BusinessConstant.WAIXIE,productionTask.getCompletedQuantity(),null,previousStatus);


                    if(!HuatekTools.isEmpty(productionTask.getApsSchedulePlanStepId())){
                        //外协暂停
                        productionTaskMapper.updateApsSchedulePlanStepStateByStepId(productionTask.getApsSchedulePlanStepId(),
                                DicConstant.ProductionOrder.SCHEDULESTATE_STATUS_WAIXIEZANTING);
                    }


                    successCount++;
                    successIds.add(id);
                } else {
                    failCount++;
                    failIds.add(id);
                }
            } catch (Exception e) {
                failCount++;
                failIds.add(id);
                log.error(e.getMessage(), e);
                throw new ServiceException(e.getMessage());
            }
        }

        TorchResponse response = new TorchResponse();

        // 根据处理结果数量返回不同的消息
        if (idList.size() == 1) {
            // 单条数据处理
            if (successCount == 1) {
//                response.setStatus(Constant.REQUEST_SUCCESS);
//                response.setMessage("外协任务设置成功");
            } else {
                //throw new ServiceException("外协任务设置失败,请联系管理员");
            }
        } else {
            // 批量数据处理
            if (successCount == idList.size()) {
//                response.setStatus(Constant.REQUEST_SUCCESS);
//                response.setMessage("批量外协任务设置成功");
                log.info(String.format("成功处理 %d 条外协任务", successCount));
            } else if (successCount > 0 && failCount > 0) {
//                response.setStatus(Constant.REQUEST_SUCCESS);
//                response.setMessage("批量外协任务部分成功");
                log.info(String.format("成功处理 %d 条，失败 %d 条。成功ID: %s，失败ID: %s",
                        successCount, failCount, String.join(",", successIds), String.join(",", failIds)));
            } else {
//                response.setStatus(Constant.REQUEST_SUCCESS);
//                response.setMessage("批量外协任务设置失败");
                log.info(String.format("所有 %d 条外协任务处理失败", failCount));
            }
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveBatch(List<ProductionTaskDTO> productionTaskDtoList) {
        for (ProductionTaskDTO productionTaskDto : productionTaskDtoList) {
            ProductionTask entity = new ProductionTask();
            BeanUtils.copyProperties(productionTaskDto, entity);
            entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

            TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
            entity.setTaskNumber(response.getData().getData().toString());
            entity.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI);
            productionTaskMapper.insert(entity);

        }

        TorchResponse response = new TorchResponse();

        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveBatchAPS(List<ProductionTaskAPSDTO> productionTaskAPSDTOList) {
        log.info("开始生成生产任务" + JSON.toJSONString(productionTaskAPSDTOList));
        List<ProductionTaskDTO> productionTaskList = new ArrayList<>();
        List<String> ids = productionTaskAPSDTOList.stream().map(ProductionTaskAPSDTO::getWorkOrderId).distinct().collect(Collectors.toList());

        ids.forEach(id -> {
            ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
            ProductList productList = productListMapper.selectById(order.getProduct());
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("work_order", id);
            CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);
            CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
            customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
            wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", scheme.getId());
            List<CustomerExperimentProjectVO> customerEperimentProjects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);

            //查询当前工单的关联工单的工序方案
//            wrapper.clear();
//            List<CustomerExperimentProject> relateExperimentProject;
//            if (!StringUtils.isEmpty(order.getRelatedWorkOrder())) {
//                wrapper.eq("work_order", order.getRelatedWorkOrder());
//                CustomerProcessScheme scheme1 = customerProcessSchemeMapper.selectOne(wrapper);
//                wrapper.clear();
//                wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", scheme1.getId());
//                relateExperimentProject = customerExperimentProjectMapper.selectList(wrapper);
//            } else {
//                relateExperimentProject = new ArrayList<>();
//            }

            List<ProductionTaskAPSDTO> tempList = productionTaskAPSDTOList.stream().filter(productionTaskAPSDTO ->
                    productionTaskAPSDTO.getWorkOrderId().equals(id)).collect(Collectors.toList());

            for (ProductionTaskAPSDTO productionTaskApsDTO : tempList) {
                CustomerExperimentProjectVO x = customerEperimentProjects.stream().filter(customerExperimentProjectVO1 ->
                        customerExperimentProjectVO1.getProcessId().equals(productionTaskApsDTO.getProcessId()) && customerExperimentProjectVO1.getDisplayNumber().equals(productionTaskApsDTO.getDisplayNumber())).findFirst().orElse(null);
                if (null == x) {
                    String msg = String.format("APS生成任务失败，未找到客户试验项目，工单号：%s，工序编码：%s，显示序号：%s", order.getWorkOrderNumber(), productionTaskApsDTO.getProcessId(), productionTaskApsDTO.getDisplayNumber());
                    throw new ServiceException(msg);
                }
                ProductionTaskDTO task = new ProductionTaskDTO();
                task.setWorkOrderNumber(order.getWorkOrderNumber());
                task.setInspectionQuantity2(order.getQuantity());
                task.setProcessName2(x.getProcessCode3Name());
                task.setProcessCode(x.getProcessCode3());
                task.setTestBasis(x.getTestBasis());
                task.setOrderNumber(order.getOrderNumber());
                task.setDisplayNumber(x.getDisplayNumber());
                task.setResponsiblePerson(order.getResponsiblePerson());
                task.setCustomerProcessName(x.getCustomerProcessName());
                task.setTestConditions(x.getTestConditions());
                task.setJudgmentCriteria(x.getJudgmentCriteria());
                task.setTicketLevel(productList.getTaskLevel());

                //打印日志计划时间 格式化下
                log.info("APS生成任务,工单号：{}，工序编码：{}，显示序号：{}，计划开始时间：{}，计划结束时间：{}",
                        order.getWorkOrderNumber(), x.getProcessCode3(), x.getDisplayNumber(), productionTaskApsDTO.getScheduledStartTime(),
                        productionTaskApsDTO.getScheduledEndTime());


                task.setScheduledStartTime(DateTimeUtil.toTimestamp(productionTaskApsDTO.getScheduledStartTime()));
                task.setScheduledEndTime(DateTimeUtil.toTimestamp(productionTaskApsDTO.getScheduledEndTime()));


                task.setApsSchedulePlanStepId(productionTaskApsDTO.getStepId());

                task.setProductName(productList.getProductName());
                task.setWorkstation(x.getWorkstation());
                task.setProductModel(productList.getProductModel());
                task.setProductCategory(productList.getProductCategory());
                task.setManufacturer(productList.getManufacturer());
                task.setBatchNumber(productList.getProductionBatch());
                task.setEntrustedUnit(scheme.getEntrustedUnit());
                task.setGrouping(x.getGrouping());
                task.setTestMethodology(x.getTestMethodology());
                task.setTestType(productList.getTestType());
                task.setExecutionSequence(x.getExecutionSequence());
                task.setCustomerExperimentProjectId(x.getId());

//                //TODO 前置工序序号
//                //查询这个工单的关联工单工序方案
//                if (relateExperimentProject.size() > 0) {
//                    Optional<CustomerExperimentProject> firstMatchedProject = relateExperimentProject.stream().filter(r -> r.getAssoWoPredProc().equals(x.getAssoWoPredProc())).findFirst();
//                    if (firstMatchedProject.isPresent()) {
//                        task.setPreExecutionSequence(firstMatchedProject.get().getExecutionSequence());
//                    }
//                }
                //获取是否审批
                StandardProcessManagement currentStandardProcessManagement = standardProcessManagementMapper.selectById(x.getProcessId());
                if (!ObjectUtils.isEmpty(currentStandardProcessManagement)) {
                    task.setIsApproval(currentStandardProcessManagement.getIsApproval());
                }
                task.setAssoWoPredProc(x.getAssoWoPredProc());
                task.setPredecessorWorkOrder(order.getPredecessorWorkOrder());
//                StandardProcessManagement standardProcessManagement = standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
//                if(!ObjectUtils.isEmpty(standardProcessManagement)){
//                    task.setAssoWoPredProc(standardProcessManagement.getProcessName2());
//                }
                ProductionOrder relateOrder = awaitingProductionOrderMapper.selectById(order.getRelatedWorkOrder());
                if (!ObjectUtils.isEmpty(relateOrder))
                    task.setRelatedWorkOrder(order.getRelatedWorkOrder());
                task.setBelongingTeam2(x.getTestingTeam());
                if (StringUtils.isNotEmpty(x.getTestingTeam())) {
                    SysGroup testTeam = sysGroupService.findGroupsByCode(x.getTestingTeam());
                    TorchResponse<SysGroupVO> group = sysGroupService.findGroup(testTeam.getGroupParentId());
                    task.setDepartment(group.getData().getData().getId());
                }
                productionTaskList.add(task);
            }

        });

        saveBatch(productionTaskList);

        //保存额更新aps
        //update aps_schedule_plan state = 1 where id in () and isdeleted  = 0;
        //update aps_schedule_plan_step state = 1 where scheduleid in () and isdeleted  = 0;
        List<String> scheduleIds = productionTaskAPSDTOList.stream().map(ProductionTaskAPSDTO::getScheduleId).distinct().collect(Collectors.toList());
        productionTaskMapper.updateApsSchedulePlanState(scheduleIds);
        productionTaskMapper.updateApsSchedulePlanStepState(scheduleIds);


        TorchResponse response = new TorchResponse();

        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse splitOrder(List<SplitOrderProductionTaskDTO> splitOrderProductionTaskDTOList) {
        log.info("分单数据:"+JSON.toJSONString(splitOrderProductionTaskDTOList));
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        String currentUserGroupId = SecurityContextHolder.getCurrentUserGroupId();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        List<ProductionTask> originalTaskList = new ArrayList<>();
        Integer totalSplitQuantity = 0;

        // 先计算总的分单数量，验证合格数量是否足够
        for (SplitOrderProductionTaskDTO splitDto : splitOrderProductionTaskDTOList) {
            if (DicConstant.CommonDic.DEFAULT_ONE.equals(splitDto.getIsSplit())) {
                totalSplitQuantity += splitDto.getInspectionQuantity();
            }
        }

        //原单
        SplitOrderProductionTaskDTO first = splitOrderProductionTaskDTOList.stream().filter(splitDto ->
                DicConstant.CommonDic.DEFAULT_ZERO.equals(splitDto.getIsSplit())).findFirst().orElse(null);

        originalTaskList = productionTaskMapper.selectList(
                new QueryWrapper<ProductionTask>()
                        .eq("work_order_number", first.getWorkOrderNumber())
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );

        //未找到任务直接返回 找到了就分任务
        if (originalTaskList.size() > 0) {

            //处理原任务
            for (ProductionTask originalTask : originalTaskList) {
                // 获取原任务的合格数量
                Integer originalQualified = originalTask.getQualifiedQuantity() != null ? originalTask.getQualifiedQuantity() : 0;
                Integer originalUnqualified = originalTask.getUnqualifiedQuantity() != null ? originalTask.getUnqualifiedQuantity() : 0;

                // 验证合格数量是否足够分单  如果合格数量还没填就该怎么分就怎么分
                if (originalTask.getQualifiedQuantity() != null && originalQualified < totalSplitQuantity) {
                    throw new ServiceException("合格数量不足，无法完成分单操作。当前合格数量：" + originalQualified + "，需要分单数量：" + totalSplitQuantity);
                }

                // 更新原任务的送检数量
                originalTask.setInspectionQuantity2(first.getInspectionQuantity());

                // 更新原任务的合格数量（减去分出的数量）
                if (originalTask.getQualifiedQuantity() != null) {
                    if (originalQualified - totalSplitQuantity > 0) {
                        originalTask.setQualifiedQuantity(originalQualified - totalSplitQuantity);
                    }
                }


                // 不合格数量保持不变
                if (originalTask.getUnqualifiedQuantity() != null) {
                    originalTask.setUnqualifiedQuantity(originalUnqualified);
                }

                // 重新计算完成数量
                if (originalTask.getQualifiedQuantity() != null && originalTask.getUnqualifiedQuantity() != null) {
                    originalTask.setCompletedQuantity(originalTask.getQualifiedQuantity() + originalTask.getUnqualifiedQuantity());
                }


                originalTask.setCodexTorchUpdateDatetime(currentTime);
                productionTaskMapper.updateById(originalTask);
            }


            //处理新工单任务
            for (SplitOrderProductionTaskDTO splitDto : splitOrderProductionTaskDTOList) {
                if (DicConstant.CommonDic.DEFAULT_ONE.equals(splitDto.getIsSplit())) {

                    if (originalTaskList.size() > 0) {
                        for (ProductionTask originalTask : originalTaskList) {
                            ProductionTask newTask = new ProductionTask();
                            BeanUtils.copyProperties(originalTask, newTask);

                            // 重置主键和关键字段
                            newTask.setId(null);

                            // 生成新的任务编号
                            TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
                            newTask.setTaskNumber(response.getData().getData().toString());

                            // 设置新任务的送检数量
                            newTask.setInspectionQuantity2(splitDto.getInspectionQuantity());

                            // 新任务的合格数量等于分单数量（从原任务的合格数量中分出）
                            newTask.setQualifiedQuantity(splitDto.getInspectionQuantity());

                            // 新任务的不合格数量为0（只分配合格数量）
                            newTask.setUnqualifiedQuantity(0);

                            // 新任务的完成数量等于合格数量
                            newTask.setCompletedQuantity(splitDto.getInspectionQuantity());

                            // 清空不合格编号（因为没有不合格数量）
                            newTask.setNonConformityNumber(null);

                            newTask.setWorkOrderNumber(splitDto.getWorkOrderNumber());
                            // 设置审计字段
                            newTask.setCodexTorchCreatorId(currentUserId);
                            newTask.setCodexTorchGroupId(currentUserGroupId);
                            newTask.setCodexTorchCreateDatetime(currentTime);
                            newTask.setCodexTorchUpdateDatetime(currentTime);
                            newTask.setCodexTorchDeleted(Constant.DEFAULT_NO);

                            // 插入新的生产任务
                            productionTaskMapper.insert(newTask);

                            //取消和完成复制报工数据
                            if(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO.equals(originalTask.getStatus()) ||
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG.equals(originalTask.getStatus())){
                                // 复制子表数据
                                copyChildTableData(originalTask.getTaskNumber(), newTask.getTaskNumber(), currentUserId, currentUserGroupId, currentTime);
                            }
                            else{
                                //除了取消和完成其他状态都改为未开始,并且不复制报工数据
                                newTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI);

                                //技术能力编号
                                newTask.setTechnicalCompetencyNumber("");
                                //操作卡
                                newTask.setOperationCard("");
                                //备注
                                newTask.setComment("");
                                //合格数量
                                newTask.setQualifiedQuantity(null);
                                //不合格数量
                                newTask.setUnqualifiedQuantity(null);
                                //完成数量
                                newTask.setCompletedQuantity(null);


                            }
                        }
                    }
                }
            }

        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    /**
     * 复制子表数据
     */
    private void copyChildTableData(String originalTaskNumber, String newTaskNumber, String currentUserId, String
            currentUserGroupId, Timestamp currentTime) {
        // 复制附件表数据
        List<ProductionTaskAttachments> attachmentsList = productionTaskAttachmentsMapper.selectList(
                new QueryWrapper<ProductionTaskAttachments>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProductionTaskAttachments attachment : attachmentsList) {
            ProductionTaskAttachments newAttachment = new ProductionTaskAttachments();
            BeanUtils.copyProperties(attachment, newAttachment);
            newAttachment.setId(null);
            newAttachment.setTaskNumber(newTaskNumber);
            newAttachment.setCodexTorchCreatorId(currentUserId);
            newAttachment.setCodexTorchGroupId(currentUserGroupId);
            newAttachment.setCodexTorchCreateDatetime(currentTime);
            newAttachment.setCodexTorchUpdateDatetime(currentTime);
            newAttachment.setCodexTorchDeleted(Constant.DEFAULT_NO);
            productionTaskAttachmentsMapper.insert(newAttachment);
        }

        // 复制试验数据表数据
        List<ProductionTaskTestData> testDataList = productionTaskTestDataMapper.selectList(
                new QueryWrapper<ProductionTaskTestData>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProductionTaskTestData testData : testDataList) {
            ProductionTaskTestData newTestData = new ProductionTaskTestData();
            BeanUtils.copyProperties(testData, newTestData);
            newTestData.setId(null);
            newTestData.setTaskNumber(newTaskNumber);
            newTestData.setCodexTorchCreatorId(currentUserId);
            newTestData.setCodexTorchGroupId(currentUserGroupId);
            newTestData.setCodexTorchCreateDatetime(currentTime);
            newTestData.setCodexTorchUpdateDatetime(currentTime);
            newTestData.setCodexTorchDeleted(Constant.DEFAULT_NO);
            productionTaskTestDataMapper.insert(newTestData);
        }

        // 复制设备信息表数据
        List<ProdTaskEqInfo> eqInfoList = prodTaskEqInfoMapper.selectList(
                new QueryWrapper<ProdTaskEqInfo>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProdTaskEqInfo eqInfo : eqInfoList) {
            ProdTaskEqInfo newEqInfo = new ProdTaskEqInfo();
            BeanUtils.copyProperties(eqInfo, newEqInfo);
            newEqInfo.setId(null);
            newEqInfo.setTaskNumber(newTaskNumber);
            newEqInfo.setCodexTorchCreatorId(currentUserId);
            newEqInfo.setCodexTorchGroupId(currentUserGroupId);
            newEqInfo.setCodexTorchCreateDatetime(currentTime);
            newEqInfo.setCodexTorchUpdateDatetime(currentTime);
            newEqInfo.setCodexTorchDeleted(Constant.DEFAULT_NO);
            prodTaskEqInfoMapper.insert(newEqInfo);
        }

        // 复制操作历史表数据
        List<ProdTaskOpHist> opHistList = prodTaskOpHistMapper.selectList(
                new QueryWrapper<ProdTaskOpHist>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProdTaskOpHist opHist : opHistList) {
            ProdTaskOpHist newOpHist = new ProdTaskOpHist();
            BeanUtils.copyProperties(opHist, newOpHist);
            newOpHist.setId(null);
            newOpHist.setTaskNumber(newTaskNumber);
            newOpHist.setCodexTorchCreatorId(currentUserId);
            newOpHist.setCodexTorchGroupId(currentUserGroupId);
            newOpHist.setCodexTorchCreateDatetime(currentTime);
            newOpHist.setCodexTorchUpdateDatetime(currentTime);
            newOpHist.setCodexTorchDeleted(Constant.DEFAULT_NO);
            prodTaskOpHistMapper.insert(newOpHist);
        }
    }

    @Override
    public TorchResponse outsourcingPass(String processId) {
        //通过id查询,把状态变更为已外协，试验方式更新为外协
        ProductionTask productionTask = productionTaskMapper.selectById(processId);
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_YIWAIXIE);
        productionTask.setTestMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING);
        productionTaskMapper.updateById(productionTask);

        insertOperationHistory(productionTask.getTaskNumber(),DicConstant.ProductionOrder.OPERATION_TYPE_WAIXIE,
                null,productionTask.getCompletedQuantity(),"外协审批通过",productionTask.getStatus());

        //更新aps已外协
        if(!HuatekTools.isEmpty(productionTask.getApsSchedulePlanStepId())){
            ProductionTaskVO productionTaskVO2 = new ProductionTaskVO();
            BeanUtils.copyProperties(productionTask, productionTaskVO2);
            startUpdateAPS(productionTaskVO2);
            productionTaskMapper.updateApsSchedulePlanStepStateByStepId(productionTask.getApsSchedulePlanStepId(),
                    DicConstant.ProductionOrder.SCHEDULESTATE_STATUS_YIWAIXIE);
        }
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse outsourcingAccept(String processId) {
        //通过id查询,把状态变更为已外协，试验方式更新为外协
        ProductionTask productionTask = productionTaskMapper.selectById(processId);
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);
        productionTaskMapper.updateById(productionTask);



        insertOperationHistory(productionTask.getTaskNumber(),DicConstant.ProductionOrder.OPERATION_TYPE_WANCHENG,
                null,productionTask.getCompletedQuantity(),"外协验收通过",productionTask.getStatus());

        commonComplete(productionTask,true);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse scanCode(ScanCodeDTO scanCodeDto) {
//        if (HuatekTools.isEmpty(scanCodeDto.getScannerGunNumber())) {
//            throw new ServiceException("扫码枪编号不能为空");
//        }
//        if (HuatekTools.isEmpty(scanCodeDto.getWorkOrderNumber())) {
//            throw new ServiceException("工单编号不能为空");
//        }
        if (HuatekTools.isEmpty(scanCodeDto.getNumber())) {
            throw new ServiceException("编号不能为空");
        }
        if (scanCodeDto.getNumber().length() < 2) {
            throw new ServiceException("编号长度必须大于2");
        }
        String workOrderNumber = scanCodeDto.getNumber().substring(0, scanCodeDto.getNumber().length() - 1);
        String scannerGunNumber = scanCodeDto.getNumber().substring(scanCodeDto.getNumber().length() - 1);
        scanCodeDto.setWorkOrderNumber(workOrderNumber);
        scanCodeDto.setScannerGunNumber(scannerGunNumber);


        TorchResponse response = new TorchResponse();

        // 1. 根据扫码枪编号查询工作站信息
        TorchResponse<WorkstationVO> workstationResponse = workstationService.findWorkstationByScannerGunNumber(scanCodeDto.getScannerGunNumber());
        WorkstationVO workstation = workstationResponse.getData().getData();


        // 2. 用工作站和工单编号查询到productionTask信息
        ProductionTaskDTO taskQuery = new ProductionTaskDTO();
        taskQuery.setWorkstation(workstation.getId());
        taskQuery.setWorkOrderNumber(scanCodeDto.getWorkOrderNumber());
        taskQuery.setToDoOrAll(BusinessConstant.PRODUCTIONTASK_TODO);


        List<ProductionTaskVO> taskList = selectProductionTaskList(taskQuery);
        //findProductionTaskPage(taskQuery);
        //List<ProductionTaskVO> taskList = taskListResponse.getData().getData();

        if (taskList == null || taskList.isEmpty()) {
            throw new ServiceException("未找到对应的生产任务");
        }


        //按照执行顺序排序
        taskList.sort(Comparator.comparing(ProductionTaskVO::getExecutionSequence));

        //拿到第一个
        ProductionTaskVO productionTask = taskList.get(0);

        String previousStatus = productionTask.getStatus();

        // 3. 检查ProductionTask状态，若任务已经取消或暂停，开始任务时需提醒，无法开始任务
        if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO.equals(productionTask.getStatus()) ||
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
            throw new ServiceException("任务已取消或暂停，无法开始任务");
        }
        //进行中返回id，前端打开报工页面  否则为空  表示开始成功 前端刷新页面
        if(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG.equals(productionTask.getStatus())){
            response.getData().setData(productionTask.getId());
            response.setStatus(Constant.REQUEST_SUCCESS);
            return response;
        }

        // 4. 判断任务状态和前置条件
        if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI.equals(productionTask.getStatus())) {
            // 状态为未开始并且上一步工序和关联工单前置工序已经完成时，扫描条码，将状态修改为进行中
            checkPreviousProcessCompleted(productionTask);


            // 将状态修改为进行中
            ProductionTaskDTO updateDto = new ProductionTaskDTO();
            updateDto.setId(productionTask.getId());
            updateDto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
            updateDto.setActualStartTime(new Timestamp(System.currentTimeMillis()));
            saveOrUpdate(updateDto);
            startUpdateAPS(productionTask);
            // 插入操作记录
            insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_START,
                    null, 0, null, previousStatus);


        } else {
            // 其他状态时，扫描条码，不修改状态，弹出报工页面，任务状态默认进行中
            //response.setMessage("显示报工页面");
        }

        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    private void startUpdateAPS(ProductionTaskVO productionTask) {
        // 1.生产任务开始的时候更新aps_schedule_plan_step中的actualbegintime为production_task的actual_start_time，state为2
        if (!HuatekTools.isEmpty(productionTask.getApsSchedulePlanStepId())) {
            productionTaskMapper.updateApsSchedulePlanStepOnTaskStart(
                    productionTask.getApsSchedulePlanStepId(),
                    productionTask.getActualStartTime());
        }
        //如果是第一道工序就更新状态为进行中
        if (productionTask.getExecutionSequence() == 1) {
            log.info("第一个工序，修改aps_schedule_plan状态为进行中,workordernumber:%s", productionTask.getWorkOrderNumber());
            productionTaskMapper.updateApsSchedulePlan(productionTask.getWorkOrderNumber(), DicConstant.ProductionOrder.SCHEDULESTATE_STATUS_JINXINGZHONG);
        }
    }

    @Override
    public List<ProductionTaskViewVO> selectProductionTaskByProductionOrder(String workOrderNumber) {

        List<ProductionTaskViewVO> list = productionTaskMapper.selectProductionTaskByProductionOrder(workOrderNumber);
        if (ObjectUtils.isEmpty(list) || list.isEmpty()) {
            list = new ArrayList<>();
            return list;
        }
//        List<String> taskNums = list.stream().filter(Objects::nonNull).map(ProductionTaskViewVO::getTaskNumber).collect(Collectors.toList());
//        if (taskNums != null && taskNums.size() > 0) {
//            //查询任务设备
//            QueryWrapper wrapper = new QueryWrapper();
//            wrapper.in("task_number", taskNums);
//            List<ProdTaskEqInfo> eqlist = prodTaskEqInfoMapper.selectList(wrapper);
//            List<ProdTaskEqInfoVO> eqvos = new ArrayList<>();
//            eqlist.stream().forEach(x -> {
//                ProdTaskEqInfoVO vo = new ProdTaskEqInfoVO();
//                BeanUtils.copyProperties(x, vo);
//                eqvos.add(vo);
//            });
//            //查询试验数据
//            List<ProductionTaskTestData> testdatas = productionTaskTestDataMapper.selectList(wrapper);
//            List<ProductionTaskTestDataVO> testdatavos = new ArrayList<>();
//            testdatas.stream().forEach(x -> {
//                ProductionTaskTestDataVO vo = new ProductionTaskTestDataVO();
//                BeanUtils.copyProperties(x, vo);
//                testdatavos.add(vo);
//            });
//
//            list.stream().forEach(x -> {
//                List<String> eqNum = eqlist.stream().filter(eq -> x.getTaskNumber().equals(eq.getTaskNumber())).map(ProdTaskEqInfo::getDeviceSerialNumber).collect(Collectors.toList());
//                x.setDeviceNumbers(String.join(",", eqNum));
//                x.setProdTaskEqInfoList(eqvos);
//                TorchResponse<List<AttachmentVO>> attachmentResponse = attachmentService.selectAttachmentListByWorkOrderAndProcess(
//                        workOrderNumber, x.getProcessCode());
//                if (attachmentResponse.getStatus() == Constant.REQUEST_SUCCESS && !CollectionUtils.isEmpty(attachmentResponse.getData().getData())) {
//                    x.setAttachmentVOList(attachmentResponse.getData().getData());
//                }
//                // 查询操作历史
//                ProdTaskOpHistDTO opHistDTO = new ProdTaskOpHistDTO();
//                opHistDTO.setTaskNumber(x.getTaskNumber());
//                List<ProdTaskOpHistVO> opHistList = prodTaskOpHistMapper.selectProdTaskOpHistList(opHistDTO);
//                x.setTaskOpHistDTOS(opHistList);
//                x.setTaskTestData(testdatavos.stream().filter(t -> t.getTaskNumber().equals(x.getTaskNumber())).collect(Collectors.toList()));
//            });
//        }
        return list;
    }

    /**
     * 检查前置工序,关联工单的前置工序,前置工单是否已完成
     *
     * @param productionTask 生产任务
     * @return 是否可以开始
     */
    private void checkPreviousProcessCompleted(ProductionTaskVO productionTask) {
        // 1.检查上一步工序：
        if (productionTask.getExecutionSequence() != null && productionTask.getExecutionSequence() > 1) {
            // 直接查询前一个工序的状态
            String prevProcessStatus = productionTaskMapper.checkPreviousProcessStatus(
                    productionTask.getWorkOrderNumber(),
                    productionTask.getExecutionSequence()
            );

            if (prevProcessStatus != null) {
                // 如果找到前一个工序，检查其状态是否为已完成
                if (!DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG.equals(prevProcessStatus)) {
                    throw new ServiceException("任务" + productionTask.getTaskNumber() + "上一步工序未完成，无法开始任务");
                }
            } else {
                // 如果没有找到上一步工序，说明前置工序不存在，可以开始
                log.warn("未找到上一步工序，工单号：{}，执行顺序：{}", productionTask.getWorkOrderNumber(), productionTask.getExecutionSequence() - 1);
            }
        }


        // 2.检查关联工单前置工序
        if (!HuatekTools.isEmpty(productionTask.getRelatedWorkOrder()) &&
                !HuatekTools.isEmpty(productionTask.getAssoWoPredProc()) &&
                !HuatekTools.isEmpty(productionTask.getPreExecutionSequence())) {

            String[] assoWoPredProcs = productionTask.getAssoWoPredProc().split(",");
            if(assoWoPredProcs.length>0){
                QueryWrapper<ProductionTask> queryWrapper=new QueryWrapper<>();
                queryWrapper.in("customer_experiment_project_id",assoWoPredProcs);
                queryWrapper.eq("codex_torch_deleted",DicConstant.CommonDic.DIC_NO);
                queryWrapper.ne("status",DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);

                Long count = productionTaskMapper.selectCount(queryWrapper);
                if(count>0){
                    log.warn("任务" + productionTask.getTaskNumber() + "的前置工序未完成，无法开始任务");
                    throw new ServiceException("任务" + productionTask.getTaskNumber() + "的前置工序未完成，无法开始任务");
                }

            }

        }
        //3.判断前置工单是否完成
        if (!HuatekTools.isEmpty(productionTask.getPredecessorWorkOrder())) {
            List<ProductionOrder> list = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>()
                    .eq("work_order_number", productionTask.getPredecessorWorkOrder())
                    .eq("codex_torch_deleted", Constant.DEFAULT_NO)
            );
            if (list != null && list.size() > 0) {
                if (!DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE.equals(list.get(0).getWorkOrderStatus())) {
                    throw new ServiceException("任务" + productionTask.getTaskNumber() + "前置工单未完成，无法开始任务");
                }
            }

        }
    }

    /**
     * 插入操作记录
     *
     * @param taskNumber        任务编号
     * @param operationType     操作类型
     * @param reason            原因
     * @param completedQuantity 已完成数量
     * @param comment           备注
     * @param previousStatus    上一个状态
     */
    private void insertOperationHistory(String taskNumber, String operationType, String reason,
                                        Integer completedQuantity, String comment, String previousStatus) {
        try {
            ProdTaskOpHistDTO histDto = new ProdTaskOpHistDTO();
            histDto.setTaskNumber(taskNumber);
            histDto.setOperationType(operationType);
            histDto.setReason(reason);
            histDto.setCompletedQuantity(completedQuantity);
            histDto.setComment(comment);
            histDto.setPreviousStatus(previousStatus);  // 设置上一个状态
            prodTaskOpHistService.saveOrUpdate(histDto);
        } catch (Exception e) {
            log.error("插入操作记录失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse startTask(List<String> taskIds) {
        if (HuatekTools.isEmpty(taskIds) || taskIds.isEmpty()) {
            throw new ServiceException("任务ID列表不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String taskId : taskIds) {
//            try {
                // 1. 根据ID查询生产任务信息
                ProductionTask productionTask = productionTaskMapper.selectById(taskId);
                if (productionTask == null) {
                    throw new ServiceException("未找到ID为" + taskId + "的生产任务");
                }

                // 2. 检查任务状态
                if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO.equals(productionTask.getStatus()) ||
                        DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
                    throw new ServiceException("任务" + productionTask.getTaskNumber() + "已取消或暂停，无法开始任务");
                }

                String previousStatus = productionTask.getStatus();
                // 3. 判断任务状态和前置条件
                if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI.equals(productionTask.getStatus())) {
                    // 转换为VO对象用于检查前置条件
                    ProductionTaskVO productionTaskVO = new ProductionTaskVO();
                    BeanUtils.copyProperties(productionTask, productionTaskVO);

                    // 检查前置工序是否完成
                    checkPreviousProcessCompleted(productionTaskVO);


                    // 将状态修改为进行中
                    productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);


//                    if (BusinessConstant.PDA_PROCESS_NAME.equals(productionTask.getProcessName2())) {
//                        productionTask.setPda(calculatePda(productionTask));
//                    }
                    //如果是pda这道工序就计算pda值塞进pda字段里
                    if (isPDA(productionTask.getProcessCode())) {
                        productionTask.setPda(calculatePda(productionTask));

                        CustomerExperimentProject customerExperimentProject = getCustomerExperimentProject(productionTask);
                        if (customerExperimentProject != null&&customerExperimentProject.getPda() != null&&productionTask.getPda() != null) {
                                if (!HuatekTools.isEmpty(customerExperimentProject.getPda()) &&
                                        productionTask.getPda().compareTo(customerExperimentProject.getPda()) >= 0) {
                                    log.info("PDA预警：{}", productionTask.getPda());
                                    productionTask.setPdaWarning(DicConstant.CommonDic.DIC_YES);
                                } else {
                                    productionTask.setPdaWarning(DicConstant.CommonDic.DIC_NO);
                                }

                        }
                    }

                    productionTask.setActualStartTime(new Timestamp(System.currentTimeMillis()));

                    productionTaskMapper.updateById(productionTask);


                    //productionTask 转换为vo
                    ProductionTaskVO productionTaskVO2 = new ProductionTaskVO();
                    BeanUtils.copyProperties(productionTask, productionTaskVO2);
                    startUpdateAPS(productionTaskVO2);
//                    // 1.生产任务开始的时候更新aps_schedule_plan_step中的actualbegintime为production_task的actual_start_time，state为2
//                    if (!HuatekTools.isEmpty(productionTask.getApsSchedulePlanStepId())) {
//                        productionTaskMapper.updateApsSchedulePlanStepOnTaskStart(
//                                productionTask.getApsSchedulePlanStepId(),
//                                productionTask.getActualStartTime());
//                    }
//                    //如果是第一道工序就更新状态为进行中
//                    if (productionTask.getExecutionSequence() == 1) {
//                        log.info("第一个工序，修改aps_schedule_plan状态为进行中,workordernumber:%s", productionTask.getWorkOrderNumber());
//                        productionTaskMapper.updateApsSchedulePlan(productionTask.getWorkOrderNumber(), DicConstant.ProductionOrder.SCHEDULESTATE_STATUS_JINXINGZHONG);
//                    }

                    // 插入操作记录
                    insertOperationHistory(productionTask.getTaskNumber(),
                            DicConstant.ProductionOrder.OPERATION_TYPE_START, null, 0, null, previousStatus);

                    successCount++;
                } else {
                    // 对于非"未开始"状态的任务，直接算作成功但不修改状态
                    successCount++;
                }
//            } catch (Exception e) {
//                log.error("开始任务处理异常，任务ID: " + taskId, e);
//                failCount++;
//                if (errorMessages.length() > 0) {
//                    errorMessages.append(", ");
//                }
//                errorMessages.append(e.getMessage());
//            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据处理结果设置返回消息
//        if (failCount == 0) {
////            if (taskIds.size() == 1) {
////                response.setMessage("任务开始成功");
////            } else {
////                response.setMessage("批量开始成功，共处理" + successCount + "个任务");
////            }
//        } else if (successCount == 0) {
//            response.setMessage("任务开始失败: " + errorMessages.toString());
//        } else {
//            response.setMessage("批量处理完成，成功" + successCount + "个，失败" + failCount + "个。失败原因: " + errorMessages.toString());
//        }

        return response;
    }


    @NotNull
    private BigDecimal getQualifiedQuantity(String workOrderNumber, String totalNoncompliantCount) {
//        StandardProcessManagement standardProcessManagement = standardProcessManagementMapper.selectOne(new QueryWrapper<StandardProcessManagement>()
//                .eq("process_code", totalNoncompliantCount)
//        );

        ProductionTask productionTask = productionTaskMapper.selectOne(new QueryWrapper<ProductionTask>()
                .eq("work_order_number", workOrderNumber)
                .eq("customer_experiment_project_id", totalNoncompliantCount)
        );

        if(productionTask==null)
        {
            throw new ServiceException("未找到不合格数量总和/对应的生产任务");
        }

        if(productionTask.getQualifiedQuantity()==null){
            throw new ServiceException("不合格数量总和/对应的生产任务合格数量不能为空,生产任务编号:"+productionTask.getTaskNumber());
        }
        //计算合格数量
        BigDecimal qualifiedQuantity = BigDecimal.valueOf(productionTask.getQualifiedQuantity());
        return qualifiedQuantity;
    }

    @NotNull
    private BigDecimal getUnqualifiedQuantity(String workOrderNumber, String mpPdaCalcRls) {
        List<String> mpPdaCalcRlsList = Arrays.asList(mpPdaCalcRls.split(","));

//        List<StandardProcessManagement> standardProcessManagementList = standardProcessManagementMapper.selectList(new QueryWrapper<StandardProcessManagement>()
//                .in("id", mpPdaCalcRlsList)
//        );
//        List<String> processCodeList = standardProcessManagementList.stream().map(StandardProcessManagement::getStepNumber).collect(Collectors.toList());

        //通过工单编号和standardProcessManagementList中的工序编号查询ProductionTask中的不合格数量
        List<ProductionTask> productionTaskList = productionTaskMapper.selectList(new QueryWrapper<ProductionTask>()
                .eq("work_order_number", workOrderNumber)
                .in("customer_experiment_project_id", mpPdaCalcRlsList)
        );
        if(productionTaskList.size()==0){
            throw new ServiceException("未找到多工序PDA计算规则对应的生产任务");
        }
        //计算不合格数量
        BigDecimal unqualifiedQuantity = new BigDecimal(0);
        for (ProductionTask entity : productionTaskList) {
            if(entity.getUnqualifiedQuantity()==null){
                throw new ServiceException("多工序PDA计算规则不合格数量不能为空,生产任务编号:"+entity.getTaskNumber());
            }
            unqualifiedQuantity = unqualifiedQuantity.add(new BigDecimal(entity.getUnqualifiedQuantity()));


        }
        return unqualifiedQuantity;
    }

    @Override
    public TorchResponse<Long> getLastProcessQualifiedQuantity(String workOrderNumber) {
        if (HuatekTools.isEmpty(workOrderNumber)) {
            throw new ServiceException("工单编号不能为空");
        }

        // 直接从数据库查询该工单的最后一个工序的合格数量
        Long qualifiedQuantity = productionTaskMapper.getLastProcessQualifiedQuantity(workOrderNumber);

        TorchResponse<Long> result = new TorchResponse<>();
        result.getData().setData(qualifiedQuantity != null ? qualifiedQuantity : 0L);
        result.setStatus(Constant.REQUEST_SUCCESS);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitTask(ProductionTaskDTO productionTaskDto) {
        if (HuatekTools.isEmpty(productionTaskDto.getId())) {
            throw new ServiceException("任务ID不能为空");
        }
        //提交的时候完成时间不能为空
        if (null == productionTaskDto.getCompletionTime6()) {
            throw new ServiceException("完成时间不能为空");
        }
        //先保存在提交
        saveOrUpdate(productionTaskDto);


        TorchResponse<SysGroupVO> group = sysGroupService.findGroup(productionTaskDto.getDepartment());
        String groupCode = group.getData().getData().getGroupCode();
        if (DicConstant.Group.GROUP_SHENGCHAN.equals(groupCode) && DicConstant.CommonDic.DIC_YES.equals(productionTaskDto.getIsApproval())) {
            if (CollectionUtils.isEmpty(productionTaskDto.getProdTaskEqInfoList())) {
                throw new ServiceException("此生产部任务需要审批，请至少添加一个设备");
            }
        }

        // 查询任务信息
        ProductionTask task = productionTaskMapper.selectById(productionTaskDto.getId());
        if (task == null) {
            throw new ServiceException("任务不存在");
        }

        String previousStatus = task.getStatus();
        if (DicConstant.CommonDic.DIC_YES.equals(task.getIsApproval())) {
            // 更新任务状态为待审批
            task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE);
        } else {
            //无需审批直接完成
            task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);
            // 3.生产任务更新到完成状态的时候更新aps_schedule_plan_step中的state为3
            if (!HuatekTools.isEmpty(task.getApsSchedulePlanStepId())) {
                Integer reportresult = 0;
                if (task.getUnqualifiedQuantity() != null) {
                    reportresult = reportresult + productionTaskDto.getUnqualifiedQuantity();
                }
                if (task.getQualifiedQuantity() != null) {
                    reportresult = reportresult + productionTaskDto.getQualifiedQuantity();
                }
                productionTaskMapper.updateApsSchedulePlanStepOnTaskComplete(task.getApsSchedulePlanStepId(), reportresult);
            }
        }
        task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
        task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        productionTaskMapper.updateById(task);

        // 插入操作记录
        insertOperationHistory(task.getTaskNumber(),
                task.getStatus(),
                null,
                task.getCompletedQuantity(),
                null, previousStatus);


        if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG.equals(task.getStatus())) {
            commonComplete(task,false);
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务提交成功");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse approveTask(ApproveTaskDTO approveTaskDto) {
        if (HuatekTools.isEmpty(approveTaskDto.getIds()) || approveTaskDto.getIds().isEmpty()) {
            throw new ServiceException("任务ID列表不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String taskId : approveTaskDto.getIds()) {
            try {
                // 查询任务信息
                ProductionTask task = productionTaskMapper.selectById(taskId);
                if (task == null) {
                    failCount++;
                    errorMessages.append("任务ID[").append(taskId).append("]不存在; ");
                    continue;
                }
                String previousStatus = task.getStatus();

                // 更新任务状态为完成
                task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);
                // 3.生产任务更新到完成状态的时候更新aps_schedule_plan_step中的state为3
                if (!HuatekTools.isEmpty(task.getApsSchedulePlanStepId())) {
                    Integer reportresult = 0;
                    if (task.getUnqualifiedQuantity() != null) {
                        reportresult = reportresult + task.getUnqualifiedQuantity();
                    }
                    if (task.getQualifiedQuantity() != null) {
                        reportresult = reportresult + task.getQualifiedQuantity();
                    }
                    productionTaskMapper.updateApsSchedulePlanStepOnTaskComplete(task.getApsSchedulePlanStepId(), reportresult);
                }
                task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
                task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                productionTaskMapper.updateById(task);

                // 插入操作记录
                insertOperationHistory(task.getTaskNumber(),
                        DicConstant.ProductionOrder.OPERATION_TYPE_WANCHENG,
                        null,
                        task.getCompletedQuantity(),
                        null, previousStatus);

                commonComplete(task,false);

                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("任务ID[").append(taskId).append("]处理失败: ").append(e.getMessage()).append("; ");
                log.error("审批通过任务失败，任务ID: {}", taskId, e);
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据任务数量调整返回消息
        if (approveTaskDto.getIds().size() == 1) {
            // 单个任务
            if (failCount == 0) {
                response.setMessage("任务审批通过成功");
            } else {
                response.setMessage("任务审批通过失败: " + errorMessages.toString());
            }
        } else {
            // 多个任务
            if (failCount == 0) {
                response.setMessage("批量审批通过成功，共处理" + successCount + "个任务");
            } else {
                response.setMessage("批量审批通过完成，成功" + successCount + "个，失败" + failCount + "个。失败原因: " + errorMessages.toString());
            }
        }

        return response;
    }

    private void commonComplete(ProductionTask task,Boolean isWaixie) {
        //保存工序报工结果
        saveProductionOrderProcessTest(task);

        //外协不更新
        if(!isWaixie){
            //更新工單PDA
            updateProductionOrdrePdaWarning(task);
        }



        //设备设置同工序时长  更新设备表的开始时间和结束时间
        updateEquipmentTime(task);




        //如果是最后一个工序，修改工单状态为完成
        log.info("当前工序id，taskid:%s", task.getId());
        String lastId = isLastProcess(task.getId());
        log.info("查询当前任务的最后一个工序，taskid:%s", task.getId());
        if (task.getId().equals(lastId)) {
            log.info("最后一个工序，修改工单状态为完成,taskid:%s", task.getId());
            awaitingProductionOrderService.completeProductionOrder(task.getWorkOrderNumber());
            log.info("最后一个工序，修改aps_schedule_plan状态为完成,workordernumber:%s", task.getWorkOrderNumber());
            productionTaskMapper.updateApsSchedulePlan(task.getWorkOrderNumber(), DicConstant.ProductionOrder.SCHEDULESTATE_STATUS_WANCHENG);
        }


    }

    /**
     * 设备设置同工序时长  更新设备表的开始时间和结束时间
     *
     * @param task
     */
    private void updateEquipmentTime(ProductionTask task) {
        List<ProdTaskEqInfo> prodTaskEqInfoList = prodTaskEqInfoMapper.selectList(new QueryWrapper<ProdTaskEqInfo>()
                .eq("task_number", task.getTaskNumber())
                .eq("duration_of_the_same_process", DicConstant.CommonDic.DIC_YES)
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        if (!CollectionUtils.isEmpty(prodTaskEqInfoList)) {
            for (ProdTaskEqInfo eqInfo : prodTaskEqInfoList) {
                eqInfo.setStartTime5(task.getActualStartTime());
                eqInfo.setEndTime(task.getActualEndTime());
                prodTaskEqInfoMapper.updateById(eqInfo);
            }
        }
    }

    /**
     * 更新工單PDA
     *
     * @param task
     */
    private void updateProductionOrdrePdaWarning(ProductionTask task) {
        ProductionOrderDTO productionOrderDTO = new ProductionOrderDTO();
        productionOrderDTO.setWorkOrderNumber(task.getWorkOrderNumber());
        //不合格數量/送檢數量
        if (task.getUnqualifiedQuantity() != null && task.getInspectionQuantity2() != null && task.getInspectionQuantity2() != 0) {
            BigDecimal unqualified = BigDecimal.valueOf(task.getUnqualifiedQuantity());
            BigDecimal inspection = BigDecimal.valueOf(task.getInspectionQuantity2());

            productionOrderDTO.setPda(
                    unqualified.divide(inspection, 2, RoundingMode.HALF_UP)
            );
        } else {
            productionOrderDTO.setPda(BigDecimal.ZERO); // 或者其他默认值
        }
        log.info("更新工单PDA,%s", productionOrderDTO.getPda());
        awaitingProductionOrderService.updateProductionOrdrePdaWarning(productionOrderDTO);
    }

    /**
     * 保存工序报工结果
     *
     * @param task
     */
    private void saveProductionOrderProcessTest(ProductionTask task) {
        ProductionOrderProcessTestDTO productionOrderProcessTestDTO = new ProductionOrderProcessTestDTO();
        productionOrderProcessTestDTO.setWorkOrder(task.getWorkOrderNumber());
        productionOrderProcessTestDTO.setProcessCode(task.getProcessCode());

        //报工内容 task序列化
        String processData = JSON.toJSONString(task);
        productionOrderProcessTestDTO.setProcessData(processData);

        //设备数据 查询productiontask对应的prod_task_eq_info
        List<ProdTaskEqInfo> prodTaskEqInfoList = prodTaskEqInfoMapper.selectList(new QueryWrapper<ProdTaskEqInfo>()
                .eq("task_number", task.getTaskNumber())
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        String deviceData = JSON.toJSONString(prodTaskEqInfoList);
        productionOrderProcessTestDTO.setDeviceData(deviceData);

        //试验结果production_task_test_data
        List<ProductionTaskTestData> productionTaskTestDataList = productionTaskTestDataMapper.selectList(new QueryWrapper<ProductionTaskTestData>()
                .eq("task_number", task.getTaskNumber())
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        String testData = JSON.toJSONString(productionTaskTestDataList);
        productionOrderProcessTestDTO.setTestData(testData);

        productionOrderProcessTestDTO.setTaskId(task.getId());
        productionOrderProcessTestDTO.setExecutionSequence(task.getExecutionSequence());
        productionOrderProcessTestService.saveOrUpdate(productionOrderProcessTestDTO);
    }

    private String isLastProcess(String id) {
        return productionTaskMapper.isLastProcess(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse rejectTask(RejectTaskDTO rejectTaskDto) {
        if (HuatekTools.isEmpty(rejectTaskDto.getIds()) || rejectTaskDto.getIds().isEmpty()) {
            throw new ServiceException("任务ID列表不能为空");
        }
        if (HuatekTools.isEmpty(rejectTaskDto.getReason())) {
            throw new ServiceException("驳回原因不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String taskId : rejectTaskDto.getIds()) {
            try {
                // 查询任务信息
                ProductionTask task = productionTaskMapper.selectById(taskId);
                if (task == null) {
                    failCount++;
                    errorMessages.append("任务ID[").append(taskId).append("]不存在; ");
                    continue;
                }
                String previousStatus = task.getStatus();

                // 更新任务状态为驳回
                task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI);
                task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
                task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                productionTaskMapper.updateById(task);

                // 插入操作记录
                insertOperationHistory(task.getTaskNumber(),
                        DicConstant.ProductionOrder.OPERATION_TYPE_BOHUI,
                        rejectTaskDto.getReason(),
                        task.getCompletedQuantity(),
                        null, previousStatus);

                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("任务ID[").append(taskId).append("]处理失败: ").append(e.getMessage()).append("; ");
                log.error("审批驳回任务失败，任务ID: {}", taskId, e);
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据任务数量调整返回消息
        if (rejectTaskDto.getIds().size() == 1) {
            // 单个任务
            if (failCount == 0) {
                response.setMessage("任务审批驳回成功");
            } else {
                response.setMessage("任务审批驳回失败: " + errorMessages.toString());
            }
        } else {
            // 多个任务
            if (failCount == 0) {
                response.setMessage("批量审批驳回成功，共处理" + successCount + "个任务");
            } else {
                response.setMessage("批量审批驳回完成，成功" + successCount + "个，失败" + failCount + "个。失败原因: " + errorMessages.toString());
            }
        }

        return response;
    }

    @Override
    public TorchResponse<List<UnqualifiedProcessVO>> getUnqualifiedTaskInfo(String workOrderNumber) {
        if (HuatekTools.isEmpty(workOrderNumber)) {
            throw new ServiceException("工单编号不能为空");
        }

        // 直接从数据库查询该工单的所有不合格工序信息
        List<UnqualifiedProcessVO> unqualifiedProcesses = productionTaskMapper.getWorkOrderQualityInfo(workOrderNumber);

        TorchResponse<List<UnqualifiedProcessVO>> result = new TorchResponse<>();
        result.getData().setData(unqualifiedProcesses != null ? unqualifiedProcesses : new ArrayList<>());
        result.setStatus(Constant.REQUEST_SUCCESS);
        return result;
    }

    @Override
    public TorchResponse pauseTask(TaskOperationDTO taskOperationDto) {
        if (HuatekTools.isEmpty(taskOperationDto.getId())) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(taskOperationDto.getId());
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        String previousStatus = productionTask.getStatus();

        // 修改状态为暂停
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING);
        productionTask.setPauseReason(taskOperationDto.getReason());

        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE,
                taskOperationDto.getReason(), taskOperationDto.getCompletedQuantity(), taskOperationDto.getComment(), previousStatus);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务已暂停");
        return response;
    }

    @Override
    public TorchResponse resumeTask(String id) {
        if (HuatekTools.isEmpty(id)) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(id);
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        // 检查当前状态是否为暂停
        if (!DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
            throw new ServiceException("任务当前状态不是暂停，无法恢复");
        }

        String currentStatus = productionTask.getStatus();

        // 从操作记录中查找最近一次暂停操作的previousStatus
        String resumeToStatus = getLastPauseOperationPreviousStatus(productionTask.getTaskNumber());

        // 如果没有找到暂停记录的previousStatus，默认恢复到进行中
        if (HuatekTools.isEmpty(resumeToStatus)) {
            resumeToStatus = DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG;
        }

        // 修改状态为恢复到的状态
        productionTask.setStatus(resumeToStatus);

        //恢复的时候如果是外协 改为自产
        if(productionTask.getTestMethodology().equals(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING)){
            //有待审批和审批通过状态的外协申请，不允许恢复，点击恢复按钮提示：当前任务存在待审批或审批通过状态的外协申请，不允许恢复。
            QueryWrapper<Outsourcing> wrapper = new QueryWrapper<>();
            wrapper.eq("process_id",id);
            wrapper.in("status",DicConstant.OutSourcingStatus.OUTSOURCING_WAITAPPROVE,DicConstant.OutSourcingStatus.OUTSOURCING_APPROVED);
            Long count= outsourcingMapper.selectCount(wrapper);
            if(count>0){
                throw new ServiceException(String.format("当前任务%s存在待审批或审批通过状态的外协申请，不允许恢复",productionTask.getTaskNumber()));
            }

            productionTask.setTestMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF);
        }
        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_RESUME,
                null, productionTask.getCompletedQuantity(), null, currentStatus);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        // response.setMessage("任务已恢复到状态：" + getStatusName(resumeToStatus));
        return response;
    }

    /**
     * 获取最近一次暂停操作的previousStatus
     *
     * @param taskNumber 任务编号
     * @return 暂停前的状态
     */
    private String getLastPauseOperationPreviousStatus(String taskNumber) {
        try {
            // 查询该任务最近一次暂停操作的记录
            QueryWrapper<ProdTaskOpHist> wrapper = new QueryWrapper<>();
            wrapper.eq("task_number", taskNumber);
            wrapper.eq("operation_type", DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE);
            // 排除上一次暂停操作的previousStatus为暂停的情况
            wrapper.ne("previous_status", DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING);
            wrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
            wrapper.orderByDesc("codex_torch_create_datetime");
            wrapper.last("LIMIT 1");

            ProdTaskOpHist lastPauseRecord = prodTaskOpHistMapper.selectOne(wrapper);
            if (lastPauseRecord != null && !HuatekTools.isEmpty(lastPauseRecord.getPreviousStatus())) {
                return lastPauseRecord.getPreviousStatus();
            }
        } catch (Exception e) {
            log.error("查询最近一次暂停操作记录失败", e);
        }
        return null;
    }

    /**
     * 获取状态名称
     *
     * @param statusCode 状态代码
     * @return 状态名称
     */
    private String getStatusName(String statusCode) {
        switch (statusCode) {
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI:
                return "未开始";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG:
                return "进行中";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE:
                return "待审批";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI:
                return "驳回";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING:
                return "暂停";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO:
                return "取消";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG:
                return "完成";
            case DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_YIWAIXIE:
                return "已外协";
            default:
                return "";
        }
    }

    @Override
    public TorchResponse cancelTask(TaskOperationDTO taskOperationDto) {
        if (HuatekTools.isEmpty(taskOperationDto.getId())) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(taskOperationDto.getId());
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        String previousStatus = productionTask.getStatus();
        // 修改状态为取消
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO);
        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_CANCEL,
                taskOperationDto.getReason(), null, taskOperationDto.getComment(), previousStatus);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务已取消");
        return response;
    }

    @Override
    public TorchResponse clearPdaWarning(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("生产任务ID列表不能为空");
        }
        UpdateWrapper<ProductionTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids);
        updateWrapper.set("pda_warning", DicConstant.CommonDic.DIC_NO);
        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
        productionTaskMapper.update(null, updateWrapper);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("PDA预警已消除");
        return response;
    }

    @Override
    public TorchResponse<List<ProductionTaskCapabilityVO>> getCapability(String productionTaskId) {
        List<ProductionTaskCapabilityVO> capabilityAssets = new ArrayList<>();
        // 1. 查询生产任务信息
        ProductionTask productionTask = productionTaskMapper.selectById(productionTaskId);
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务信息");
        }

        // 2. 获取产品型号和工序编码
        String productModel = productionTask.getProductModel();
        String processCode = productionTask.getProcessCode();

        if (StringUtils.isEmpty(productModel) || StringUtils.isEmpty(processCode)) {
            log.info("生产任务缺少产品型号或工序编码信息");
        } else {
            // 3. 查询工序分类
            String skill = productionTaskMapper.getSkillByCode(processCode);
            if (!StringUtils.isEmpty(skill)) {
                // 4. 查询能力资产
                capabilityAssets = capabilityAssetMapper.selectByProductModelAndType(
                        productModel, skill);
            }


        }


        TorchResponse<List<ProductionTaskCapabilityVO>> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(capabilityAssets);
        response.getData().setCount((long) capabilityAssets.size());

        return response;
    }

    @Override
    public TorchResponse<List<String>> getTestDataDictionary(TestDataDictionaryDTO testDataDictionaryDTO) {
        String stepNumber = testDataDictionaryDTO.getExperimentProject();
        String processId = null;
        if (!StringUtils.isEmpty(stepNumber)) {
            StandardProcessManagement spm = standardProcessManagementMapper.selectOne(
                    new QueryWrapper<StandardProcessManagement>()
                            .eq("step_number", stepNumber)
                            .eq("codex_torch_deleted", Constant.DEFAULT_NO)
            );
            processId = spm == null ? null : spm.getId();
        }

        if (StringUtils.isEmpty(processId)) {
            TorchResponse<List<String>> empty = new TorchResponse<>();
            empty.setStatus(Constant.REQUEST_SUCCESS);
            empty.getData().setData(Collections.emptyList());
            empty.getData().setCount(0L);
            return empty;
        }

        List<TestDataDictionary> testDataDictionaryList = testDataDictionaryMapper.selectList(new QueryWrapper<TestDataDictionary>()
                .eq("test_type", testDataDictionaryDTO.getTestType())
                .eq("experiment_project", processId)
                .eq("field_name", testDataDictionaryDTO.getFieldName())
        );
        //判断是否有数据没有数据再用编号去查
        if (CollectionUtils.isEmpty(testDataDictionaryList)) {
            testDataDictionaryList = testDataDictionaryMapper.selectList(new QueryWrapper<TestDataDictionary>()
                    .eq("test_type", testDataDictionaryDTO.getTestType())
                    .eq("experiment_project", stepNumber)
                    .eq("field_name", testDataDictionaryDTO.getFieldName())
            );
        }



        List<String> testDataDictionaryVOList = new ArrayList<>();
        for (TestDataDictionary testDataDictionary : testDataDictionaryList) {
            testDataDictionaryVOList.add(testDataDictionary.getDataDictionaryValue());
        }

        TorchResponse<List<String>> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(testDataDictionaryVOList);
        return response;
    }

    @Override
    public TorchResponse findDelayedTasks() {
        List<ProductionTaskVO> productionTaskVOS = productionTaskMapper.findDelayedTasks();

        TorchResponse response = new TorchResponse();
        response.getData().setData(productionTaskVOS);
        return response;
    }

    @Override
    public ProductionTaskVO selectProductionTaskByWorkOrderAndProcss(String workOrder, String processCode) {
        return productionTaskMapper.selectProductionTaskByWorkOrderAndProcss(workOrder, processCode);
    }

    /**
     * 根据生产工单集合查询有未处理的异常（工单下任务）的工单集合
     *
     * @param relatedWorkOrders 工单编号集合
     * @return 存在异常未处理的工单编号集合
     * @throws Exception
     */
    @Override
    public TorchResponse getAbnormalDetails(List<String> relatedWorkOrders) {

        //去掉空和null
        relatedWorkOrders = relatedWorkOrders.stream().filter(Objects::nonNull).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(relatedWorkOrders)) {
            TorchResponse response = new TorchResponse();
            response.getData().setData(Collections.emptyList());
            return response;
        }

        //被关联工单存的是id所以先查询出来编号
        List<String> workOrderNumbers = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>()
                .in("id", relatedWorkOrders)
        ).stream().map(ProductionOrder::getWorkOrderNumber).collect(Collectors.toList());


        LambdaQueryWrapper<Abnormalfeedback> queryWrapper = Wrappers.lambdaQuery(Abnormalfeedback.class)
                .in(Abnormalfeedback::getProductionWorkOrder, workOrderNumbers)
                .eq(Abnormalfeedback::getCodexTorchDeleted, Constant.DEFAULT_NO)
                .eq(Abnormalfeedback::getSourceModule, BusinessConstant.PRODUCTION_TASK_MANAGE_MENU_NAME)
                .eq(Abnormalfeedback::getStatus, DicConstant.Abnormalfeedback.ABNORMALFEEDBACK_STATUS_IN_PROGRESS);
        TorchResponse response = new TorchResponse();
        List<Abnormalfeedback> abnormalfeedbacks = abnormalfeedbackMapper.selectList(queryWrapper);
        List<String> abnormalfeedbackWorkOrderNumbers = new ArrayList<>();
        for (Abnormalfeedback abnormalfeedback : abnormalfeedbacks) {
            abnormalfeedbackWorkOrderNumbers.add(abnormalfeedback.getProductionWorkOrder());
        }
        response.getData().setData(abnormalfeedbackWorkOrderNumbers);
        return response;
    }

    @Override
    public TorchResponse<ProductionOrderVO> selectProductionOrderByNumber(String workOrderNumber) {
        ProductionOrderVO productionOrderVO = awaitingProductionOrderMapper.selectProductionOrderByWorkOrderNumber(workOrderNumber);
        TorchResponse<ProductionOrderVO> response = new TorchResponse<>();
        response.getData().setData(productionOrderVO);
        return response;
    }

    @Override
    public TorchResponse outsourcingReject(String taskId) {
        ProductionTask productionTask = productionTaskMapper.selectById(taskId);
        productionTask.setTestMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF);
        productionTaskMapper.updateById(productionTask);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

}
