package com.huatek.frame.modules.system.listener;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.huatek.frame.modules.system.service.DicDetailService;

import lombok.extern.slf4j.Slf4j;

/**
 * 字典缓存初始化器
 * 在应用启动完成后初始化字典缓存数据到Redis
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
@Slf4j
public class DictCacheInitializer implements ApplicationRunner {

    @Autowired
    private DicDetailService dicDetailService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("开始初始化字典缓存数据到Redis...");
            dicDetailService.loadingDictCache();
            log.info("字典缓存数据初始化完成");
        } catch (Exception e) {
            log.error("字典缓存数据初始化失败", e);
        }
    }
}
